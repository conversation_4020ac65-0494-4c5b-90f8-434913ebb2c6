-- =====================================================
-- PHASE 3 COMPREHENSIVE DATA VALIDATION
-- Verify all hotel data has been properly loaded
-- =====================================================

-- HEADER: Test Information
SELECT
    'PHASE 3 DATA VALIDATION REPORT' as report_title,
    'Generated: ' || now()::text as timestamp,
    'Target Cities: Casablanca and Marrakech' as scope;

-- =====================================================
-- SECTION 1: HOTEL DATA COMPLETENESS
-- =====================================================

SELECT '1. HOTEL DATA COMPLETENESS' as section_title;

-- 1.1: Overall hotel counts by city
SELECT
    'Hotel Counts by City' as test_name,
    c.name as city_name,
    COUNT(a.id) as hotel_count,
    COUNT(a.external_id) as hotels_with_external_id,
    COUNT(a.stars) as hotels_with_stars,
    COUNT(a.average_score) as hotels_with_ratings,
    COUNT(a.construction_year) as hotels_with_construction_year,
    COUNT(a.highlights) as hotels_with_highlights
FROM city c
LEFT JOIN location l ON c.id = l.city_id AND l.type = 'hotel'
LEFT JOIN accommodation a ON l.id = a.location_id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.id, c.name
ORDER BY c.name;

-- 1.2: Data completeness summary
SELECT
    'Data Completeness Summary' as test_name,
    COUNT(*) as total_hotels,
    COUNT(external_id) as with_external_id,
    ROUND(COUNT(external_id) * 100.0 / COUNT(*), 2) as external_id_percentage,
    COUNT(stars) as with_stars,
    ROUND(COUNT(stars) * 100.0 / COUNT(*), 2) as stars_percentage,
    COUNT(average_score) as with_ratings,
    ROUND(COUNT(average_score) * 100.0 / COUNT(*), 2) as ratings_percentage,
    COUNT(highlights) as with_highlights,
    ROUND(COUNT(highlights) * 100.0 / COUNT(*), 2) as highlights_percentage
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 1.3: Sample complete hotel records
SELECT
    'Sample Complete Hotel Records' as test_name,
    a.id,
    a.name,
    a.external_id,
    a.stars,
    a.average_score,
    a.construction_year,
    array_length(a.highlights, 1) as highlights_count,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
AND a.external_id IS NOT NULL
AND a.stars IS NOT NULL
AND a.average_score IS NOT NULL
ORDER BY a.id
LIMIT 10;

-- =====================================================
-- SECTION 2: LOCATION DATA INTEGRITY
-- =====================================================

SELECT '2. LOCATION DATA INTEGRITY' as section_title;

-- 2.1: Location completeness by city
SELECT
    'Location Data by City' as test_name,
    c.name as city_name,
    COUNT(l.id) as total_locations,
    COUNT(l.lat) as locations_with_coordinates,
    COUNT(l.name) as locations_with_names,
    COUNT(CASE WHEN l.type = 'hotel' THEN 1 END) as hotel_locations,
    AVG(l.lat) as avg_latitude,
    AVG(l.lng) as avg_longitude
FROM city c
LEFT JOIN location l ON c.id = l.city_id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.id, c.name
ORDER BY c.name;

-- 2.2: Hotels without locations (should be 0)
SELECT
    'Hotels Without Locations' as test_name,
    COUNT(*) as orphaned_hotels
FROM accommodation a
LEFT JOIN location l ON a.location_id = l.id
WHERE l.id IS NULL;

-- 2.3: Locations without accommodations (should be minimal)
SELECT
    'Locations Without Accommodations' as test_name,
    COUNT(*) as unused_locations
FROM location l
LEFT JOIN accommodation a ON l.id = a.location_id
WHERE l.type = 'hotel'
AND a.id IS NULL
AND l.city_id IN (SELECT id FROM city WHERE name IN ('Casablanca', 'Marrakech'));

-- =====================================================
-- SECTION 3: IMAGE LOADING VERIFICATION
-- =====================================================

SELECT '3. IMAGE LOADING VERIFICATION' as section_title;

-- 3.1: Image loading summary
SELECT
    'Image Loading Summary' as test_name,
    COUNT(DISTINCT i.id) as total_images,
    COUNT(DISTINCT ei.id) as total_entity_images,
    COUNT(DISTINCT ei.entity_id) as accommodations_with_images,
    COUNT(DISTINCT CASE WHEN ei.is_featured = true THEN ei.entity_id END) as accommodations_with_featured_images
FROM image i
JOIN entity_image ei ON i.id = ei.image_id
WHERE ei.entity_type = 'accommodation';

-- 3.2: Image data by city
SELECT
    'Images by City' as test_name,
    c.name as city_name,
    COUNT(DISTINCT a.id) as total_hotels,
    COUNT(DISTINCT ei.entity_id) as hotels_with_images,
    ROUND(COUNT(DISTINCT ei.entity_id) * 100.0 / COUNT(DISTINCT a.id), 2) as image_coverage_percentage,
    COUNT(ei.id) as total_image_relationships
FROM city c
JOIN location l ON c.id = l.city_id AND l.type = 'hotel'
JOIN accommodation a ON l.id = a.location_id
LEFT JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.id, c.name
ORDER BY c.name;

-- 3.3: Sample image data
SELECT
    'Sample Image Data' as test_name,
    a.name as hotel_name,
    a.external_id,
    i.url as image_url,
    ei.image_type,
    ei.is_featured,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
JOIN image i ON ei.image_id = i.id
WHERE c.name IN ('Casablanca', 'Marrakech')
ORDER BY a.id
LIMIT 10;

-- =====================================================
-- SECTION 4: PRICE FORECAST DATA
-- =====================================================

SELECT '4. PRICE FORECAST DATA' as section_title;

-- 4.1: Price forecast summary
SELECT
    'Price Forecast Summary' as test_name,
    COUNT(*) as total_price_forecasts,
    COUNT(DISTINCT accommodation_id) as accommodations_with_prices,
    COUNT(DISTINCT forecast_month) as unique_months,
    MIN(price_value) as min_price,
    MAX(price_value) as max_price,
    ROUND(AVG(price_value), 2) as avg_price,
    COUNT(DISTINCT currency) as currencies_used
FROM accommodation_price_forecast apf
JOIN accommodation a ON apf.accommodation_id = a.id
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 4.2: Price forecasts by city and month
SELECT
    'Price Forecasts by City and Month' as test_name,
    c.name as city_name,
    apf.forecast_month,
    COUNT(*) as hotel_count,
    ROUND(AVG(apf.price_value), 2) as avg_price,
    MIN(apf.price_value) as min_price,
    MAX(apf.price_value) as max_price
FROM accommodation_price_forecast apf
JOIN accommodation a ON apf.accommodation_id = a.id
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.name, apf.forecast_month
ORDER BY c.name, apf.forecast_month;

-- 4.3: Hotels without price forecasts
SELECT
    'Hotels Without Price Forecasts' as test_name,
    c.name as city_name,
    COUNT(a.id) as hotels_without_prices
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_price_forecast apf ON a.id = apf.accommodation_id
WHERE c.name IN ('Casablanca', 'Marrakech')
AND apf.id IS NULL
GROUP BY c.name
ORDER BY c.name;

-- =====================================================
-- SECTION 5: EXTERNAL ID TRACKING
-- =====================================================

SELECT '5. EXTERNAL ID TRACKING' as section_title;

-- 5.1: External ID completeness
SELECT
    'External ID Completeness' as test_name,
    c.name as city_name,
    COUNT(a.id) as total_hotels,
    COUNT(a.external_id) as hotels_with_external_id,
    ROUND(COUNT(a.external_id) * 100.0 / COUNT(a.id), 2) as external_id_coverage,
    COUNT(DISTINCT a.external_id) as unique_external_ids
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.name
ORDER BY c.name;

-- 5.2: External ID format validation
SELECT
    'External ID Format Validation' as test_name,
    COUNT(*) as total_external_ids,
    COUNT(CASE WHEN external_id NOT LIKE '%[^0-9]%' THEN 1 END) as numeric_external_ids,
    COUNT(CASE WHEN LEN(external_id) BETWEEN 5 AND 10 THEN 1 END) as reasonable_length_ids,
    MIN(LEN(external_id)) as min_id_length,
    MAX(LEN(external_id)) as max_id_length
FROM accommodation
WHERE external_id IS NOT NULL;

-- 5.3: Sample external ID mappings
SELECT
    'Sample External ID Mappings' as test_name,
    a.id as internal_id,
    a.external_id,
    a.name as hotel_name,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
AND a.external_id IS NOT NULL
ORDER BY CAST(a.external_id AS BIGINT);

-- 5.4: Duplicate external ID check (should be 0)
SELECT
    'Duplicate External IDs' as test_name,
    external_id,
    COUNT(*) as duplicate_count
FROM accommodation
WHERE external_id IS NOT NULL
GROUP BY external_id
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC;

-- =====================================================
-- SECTION 6: DATA RELATIONSHIPS
-- =====================================================

SELECT '6. DATA RELATIONSHIPS' as section_title;

-- 6.1: Foreign key relationship integrity
SELECT
    'Foreign Key Integrity Check' as test_name,
    'accommodation -> location' as relationship,
    COUNT(a.id) as total_accommodations,
    COUNT(l.id) as valid_location_references,
    COUNT(a.id) - COUNT(l.id) as broken_references
FROM accommodation a
LEFT JOIN location l ON a.location_id = l.id
UNION ALL
SELECT
    'Foreign Key Integrity Check' as test_name,
    'location -> city' as relationship,
    COUNT(l.id) as total_locations,
    COUNT(c.id) as valid_city_references,
    COUNT(l.id) - COUNT(c.id) as broken_references
FROM location l
LEFT JOIN city c ON l.city_id = c.id
WHERE l.type = 'hotel'
UNION ALL
SELECT
    'Foreign Key Integrity Check' as test_name,
    'price_forecast -> accommodation' as relationship,
    COUNT(apf.id) as total_price_forecasts,
    COUNT(a.id) as valid_accommodation_references,
    COUNT(apf.id) - COUNT(a.id) as broken_references
FROM accommodation_price_forecast apf
LEFT JOIN accommodation a ON apf.accommodation_id = a.id
UNION ALL
SELECT
    'Foreign Key Integrity Check' as test_name,
    'entity_image -> accommodation' as relationship,
    COUNT(ei.id) as total_entity_images,
    COUNT(a.id) as valid_accommodation_references,
    COUNT(ei.id) - COUNT(a.id) as broken_references
FROM entity_image ei
LEFT JOIN accommodation a ON ei.entity_id = a.id
WHERE ei.entity_type = 'accommodation';

-- 6.2: Accommodation type verification
SELECT
    'Accommodation Type Verification' as test_name,
    at.name as accommodation_type,
    COUNT(a.id) as hotel_count
FROM accommodation a
JOIN accommodation_type at ON a.type_id = at.id
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY at.id, at.name
ORDER BY COUNT(a.id) DESC;

-- =====================================================
-- SECTION 7: DATA QUALITY CHECKS
-- =====================================================

SELECT '7. DATA QUALITY CHECKS' as section_title;

-- 7.1: Missing critical data
SELECT
    'Missing Critical Data' as test_name,
    COUNT(*) as total_hotels,
    COUNT(CASE WHEN name IS NULL OR name = '' THEN 1 END) as missing_names,
    COUNT(CASE WHEN external_id IS NULL THEN 1 END) as missing_external_ids,
    COUNT(CASE WHEN stars IS NULL THEN 1 END) as missing_stars,
    COUNT(CASE WHEN average_score IS NULL THEN 1 END) as missing_ratings
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 7.2: Data range validation
SELECT
    'Data Range Validation' as test_name,
    COUNT(CASE WHEN stars < 1 OR stars > 5 THEN 1 END) as invalid_star_ratings,
    COUNT(CASE WHEN average_score < 0 OR average_score > 10 THEN 1 END) as invalid_scores,
    COUNT(CASE WHEN construction_year < 1800 OR construction_year > 2030 THEN 1 END) as invalid_construction_years,
    MIN(stars) as min_stars,
    MAX(stars) as max_stars,
    MIN(average_score) as min_score,
    MAX(average_score) as max_score
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 7.3: Coordinate validation
SELECT
    'Coordinate Validation' as test_name,
    COUNT(*) as total_locations,
    COUNT(CASE WHEN lat IS NULL OR lng IS NULL THEN 1 END) as missing_coordinates,
    COUNT(CASE WHEN lat < 30 OR lat > 36 THEN 1 END) as invalid_latitudes,
    COUNT(CASE WHEN lng < -10 OR lng > -5 THEN 1 END) as invalid_longitudes,
    ROUND(MIN(lat), 4) as min_latitude,
    ROUND(MAX(lat), 4) as max_latitude,
    ROUND(MIN(lng), 4) as min_longitude,
    ROUND(MAX(lng), 4) as max_longitude
FROM location l
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
AND l.type = 'hotel';

-- =====================================================
-- SECTION 8: SAMPLE DATA QUERIES
-- =====================================================

SELECT '8. SAMPLE DATA QUERIES' as section_title;

-- 8.1: Complete hotel records with all data
SELECT
    'Complete Hotel Records Sample' as test_name,
    a.id,
    a.name as hotel_name,
    a.external_id,
    a.stars,
    a.average_score,
    a.construction_year,
    c.name as city_name,
    l.lat,
    l.lng,
    COUNT(DISTINCT apf.id) as price_forecasts_count,
    COUNT(DISTINCT ei.id) as images_count,
    'See individual queries for details' as forecast_months,
    'See individual queries for details' as sample_prices
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_price_forecast apf ON a.id = apf.accommodation_id
LEFT JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
WHERE c.name IN ('Casablanca', 'Marrakech')
AND a.external_id IS NOT NULL
GROUP BY a.id, a.name, a.external_id, a.stars, a.average_score, a.construction_year, c.name, l.lat, l.lng
HAVING COUNT(DISTINCT apf.id) > 0 AND COUNT(DISTINCT ei.id) > 0
ORDER BY a.id;

-- 8.2: Hotels with most price forecasts
SELECT TOP 10
    'Hotels with Most Price Forecasts' as test_name,
    a.name as hotel_name,
    a.external_id,
    c.name as city_name,
    COUNT(apf.id) as price_forecast_count,
    MIN(apf.price_value) as min_price,
    MAX(apf.price_value) as max_price,
    AVG(apf.price_value) as avg_price
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
JOIN accommodation_price_forecast apf ON a.id = apf.accommodation_id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY a.id, a.name, a.external_id, c.name
ORDER BY COUNT(apf.id) DESC;

-- 8.3: Sample image URLs
SELECT TOP 10
    'Sample Image URLs' as test_name,
    a.name as hotel_name,
    a.external_id,
    i.url as image_url,
    ei.image_type,
    ei.is_featured,
    c.name as city_name
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
JOIN image i ON ei.image_id = i.id
WHERE c.name IN ('Casablanca', 'Marrakech')
ORDER BY a.id;

-- =====================================================
-- SECTION 9: FINAL SUMMARY AND RECOMMENDATIONS
-- =====================================================

SELECT '9. FINAL SUMMARY' as section_title;

-- 9.1: Overall Phase 3 completion summary
SELECT
    'Phase 3 Completion Summary' as summary_type,
    (SELECT COUNT(*) FROM city WHERE name IN ('Casablanca', 'Marrakech')) as target_cities,
    (SELECT COUNT(DISTINCT c.id)
     FROM accommodation a
     JOIN location l ON a.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')) as cities_with_hotels,
    (SELECT COUNT(*)
     FROM accommodation a
     JOIN location l ON a.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')) as total_hotels_loaded,
    (SELECT COUNT(*)
     FROM accommodation a
     JOIN location l ON a.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')
     AND a.external_id IS NOT NULL) as hotels_with_external_ids,
    (SELECT COUNT(*) FROM accommodation_price_forecast apf
     JOIN accommodation a ON apf.accommodation_id = a.id
     JOIN location l ON a.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')) as total_price_forecasts,
    (SELECT COUNT(*) FROM entity_image ei
     JOIN accommodation a ON ei.entity_id = a.id
     JOIN location l ON a.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')
     AND ei.entity_type = 'accommodation') as total_images;

-- 9.2: Data quality score
SELECT
    'Data Quality Score' as quality_metric,
    ROUND(
        (COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*))
    , 2) as external_id_completeness_pct,
    ROUND(
        (COUNT(CASE WHEN apf.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(DISTINCT a.id))
    , 2) as price_forecast_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN ei.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(DISTINCT a.id))
    , 2) as image_coverage_pct,
    CASE
        WHEN COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 95
        AND COUNT(CASE WHEN apf.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(DISTINCT a.id) >= 70
        AND COUNT(CASE WHEN ei.id IS NOT NULL THEN 1 END) * 100.0 / COUNT(DISTINCT a.id) >= 90
        THEN 'EXCELLENT - Ready for Phase 4'
        WHEN COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*) >= 90
        THEN 'GOOD - Minor issues to review'
        ELSE 'NEEDS ATTENTION - Check data loading'
    END as overall_quality_assessment
FROM accommodation a
JOIN location l ON a.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_price_forecast apf ON a.id = apf.accommodation_id
LEFT JOIN entity_image ei ON a.id = ei.entity_id AND ei.entity_type = 'accommodation'
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 9.3: Recommendations for Phase 4
SELECT
    'Phase 4 Readiness Check' as readiness_check,
    CASE
        WHEN (SELECT COUNT(*) FROM accommodation a
              JOIN location l ON a.location_id = l.id
              JOIN city c ON l.city_id = c.id
              WHERE c.name IN ('Casablanca', 'Marrakech')
              AND a.external_id IS NULL) = 0
        THEN '✅ All hotels have external IDs - Ready for amenity/review linking'
        ELSE '⚠️ Some hotels missing external IDs - May affect Phase 4 linking'
    END as external_id_status,
    CASE
        WHEN (SELECT COUNT(*) FROM accommodation_price_forecast apf
              JOIN accommodation a ON apf.accommodation_id = a.id
              JOIN location l ON a.location_id = l.id
              JOIN city c ON l.city_id = c.id
              WHERE c.name IN ('Casablanca', 'Marrakech')) > 0
        THEN '✅ Price forecasts loaded successfully'
        ELSE '❌ No price forecasts found - Check data loading'
    END as price_forecast_status,
    CASE
        WHEN (SELECT COUNT(*) FROM entity_image ei
              JOIN accommodation a ON ei.entity_id = a.id
              JOIN location l ON a.location_id = l.id
              JOIN city c ON l.city_id = c.id
              WHERE c.name IN ('Casablanca', 'Marrakech')
              AND ei.entity_type = 'accommodation') > 0
        THEN '✅ Images loaded successfully'
        ELSE '❌ No images found - Check data loading'
    END as image_status;

-- Final message
SELECT
    '🎯 PHASE 3 VALIDATION COMPLETE' as final_message,
    'Review the results above to ensure all data is properly loaded' as instruction,
    'If all checks pass, proceed to Phase 4 (Amenity and Review Relationships)' as next_step;
