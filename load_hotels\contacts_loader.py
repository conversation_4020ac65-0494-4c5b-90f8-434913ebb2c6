#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Contacts Loader
Refactored from Phase 5: Loads contact information (phone, website, address).
"""

import logging
import traceback
from typing import Dict, Optional
from datetime import datetime
try:
    from .database import DatabaseManager
    from .utils import FileManager, ProgressTracker, DataValidator
    from .config import HotelLoadingConfig
except ImportError:
    from database import DatabaseManager
    from utils import FileManager, ProgressTracker, DataValidator
    from config import HotelLoadingConfig

logger = logging.getLogger(__name__)

class ContactsLoader:
    """Contacts loader - refactored from Phase 5"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.stats = {
            'cities_processed': 0,
            'contacts_processed': 0,
            'accommodations_updated': 0,
            'locations_updated': 0,
            'phones_added': 0,
            'websites_added': 0,
            'addresses_added': 0,
            'errors': []
        }

    def update_accommodation_contact(self, accommodation_id: int, location_id: int, contact_data: Dict) -> Dict:
        """Update accommodation and location with contact information"""
        result = {
            'success': False,
            'accommodation_updated': False,
            'location_updated': False,
            'phone_added': False,
            'website_added': False,
            'address_added': False,
            'error': None
        }

        try:
            # Extract and validate contact data
            phone = DataValidator.validate_phone_number(contact_data.get('phone'))
            website = DataValidator.validate_website_url(contact_data.get('website'))
            address = DataValidator.combine_address_parts(
                contact_data.get('street_address'),
                contact_data.get('postal_code')
            )

            with self.db.get_cursor() as cursor:
                # Update accommodation with phone and website
                if phone or website:
                    update_fields = []
                    update_values = []

                    if phone:
                        update_fields.append("phone = %s")
                        update_values.append(phone)
                        result['phone_added'] = True

                    if website:
                        update_fields.append("website = %s")
                        update_values.append(website)
                        result['website_added'] = True

                    if update_fields:
                        update_fields.append("updated_at = now()")
                        update_values.append(accommodation_id)

                        update_query = f"""
                            UPDATE accommodation
                            SET {', '.join(update_fields)}
                            WHERE id = %s
                        """

                        cursor.execute(update_query, update_values)

                        if cursor.rowcount > 0:
                            result['accommodation_updated'] = True
                            logger.debug(f"    ✓ Updated accommodation contact info")

                # Update location with address
                if address:
                    cursor.execute("""
                        UPDATE location
                        SET address = %s, updated_at = now()
                        WHERE id = %s
                    """, (address, location_id))

                    if cursor.rowcount > 0:
                        result['location_updated'] = True
                        result['address_added'] = True
                        logger.debug(f"    ✓ Updated location address: {address}")

                result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"    ✗ Contact update error: {e}")

        return result

    def process_single_contact(self, external_id: str, contact_data: Dict, city_id: int) -> Dict:
        """Process contact information for a single accommodation"""
        result = {
            'success': False,
            'accommodation_updated': 0,
            'location_updated': 0,
            'phone_added': 0,
            'website_added': 0,
            'address_added': 0,
            'error': None
        }

        try:
            # Get accommodation and location IDs
            ids = self.db.get_accommodation_by_external_id(external_id, city_id)
            if not ids:
                result['error'] = f"No accommodation found for external ID {external_id}"
                return result

            accommodation_id, location_id = ids

            # Update contact information
            update_result = self.update_accommodation_contact(accommodation_id, location_id, contact_data)

            if update_result['success']:
                result['success'] = True
                result['accommodation_updated'] = 1 if update_result['accommodation_updated'] else 0
                result['location_updated'] = 1 if update_result['location_updated'] else 0
                result['phone_added'] = 1 if update_result['phone_added'] else 0
                result['website_added'] = 1 if update_result['website_added'] else 0
                result['address_added'] = 1 if update_result['address_added'] else 0
            else:
                result['error'] = update_result['error']

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"  ✗ Contact processing error: {e}")
            logger.error(traceback.format_exc())

        return result

    def process_city(self, city_key: str, city_config: Dict) -> Dict:
        """Process all contact records for a city"""
        city_stats = {
            'city_name': city_config['city_name'],
            'contacts_found': 0,
            'contacts_processed': 0,
            'accommodations_updated': 0,
            'locations_updated': 0,
            'phones_added': 0,
            'websites_added': 0,
            'addresses_added': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            logger.info("="*60)
            logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
            logger.info("="*60)

            # Load contact data for this city
            contacts_file_path = HotelLoadingConfig.get_data_file_path(city_key, 'contacts')
            contacts_data = FileManager.load_json_data(contacts_file_path)

            if not contacts_data:
                city_stats['errors'].append(f"No contact data found for {city_config['city_name']}")
                city_stats['status'] = 'failed'
                return city_stats

            city_stats['contacts_found'] = len(contacts_data)
            logger.info(f"Found {city_stats['contacts_found']} contact records in {city_config['city_name']}")

            # Initialize progress tracker
            progress = ProgressTracker(city_stats['contacts_found'])

            # Process each contact record
            for contact_index, (external_id, contact_data) in enumerate(contacts_data.items()):
                try:
                    logger.info(f"Processing contact {contact_index + 1}/{city_stats['contacts_found']}: External ID {external_id}")

                    # Process this contact record
                    result = self.process_single_contact(
                        external_id,
                        contact_data,
                        city_config['city_id']
                    )

                    if result['success']:
                        # Commit this individual contact update
                        self.db.commit()
                        city_stats['contacts_processed'] += 1
                        city_stats['accommodations_updated'] += result['accommodation_updated']
                        city_stats['locations_updated'] += result['location_updated']
                        city_stats['phones_added'] += result['phone_added']
                        city_stats['websites_added'] += result['website_added']
                        city_stats['addresses_added'] += result['address_added']
                        logger.info(f"✓ Contact processed and committed successfully")
                    else:
                        # Rollback this individual contact but continue with others
                        self.db.rollback()
                        city_stats['errors'].append(f"Contact {contact_index + 1}: {result['error']}")
                        logger.error(f"[ERROR] Contact processing failed: {result['error']}")

                    progress.update()

                except Exception as e:
                    # Rollback this individual contact but continue with others
                    self.db.rollback()
                    error_msg = f"Contact {contact_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    city_stats['errors'].append(error_msg)

            city_stats['status'] = 'completed'
            logger.info(f"[OK] {city_config['city_name']} processing completed")
            logger.info(f"  Contacts processed: {city_stats['contacts_processed']}/{city_stats['contacts_found']}")
            logger.info(f"  Accommodations updated: {city_stats['accommodations_updated']}")
            logger.info(f"  Locations updated: {city_stats['locations_updated']}")
            logger.info(f"  Phones added: {city_stats['phones_added']}")
            logger.info(f"  Websites added: {city_stats['websites_added']}")
            logger.info(f"  Addresses added: {city_stats['addresses_added']}")
            logger.info(f"  Errors: {len(city_stats['errors'])}")

        except Exception as e:
            # City-level error
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def load_contacts(self, target_cities: Dict) -> Dict:
        """Load contact data for all target cities"""
        logger.info("🚀 STARTING CONTACT DATA LOADING")
        logger.info("="*80)

        start_time = datetime.now()
        results = {'cities': {}, 'overall': self.stats.copy()}

        try:
            # Process each target city
            for city_key, city_config in target_cities.items():
                city_result = self.process_city(city_key, city_config)
                results['cities'][city_key] = city_result

                # Update overall statistics
                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['contacts_processed'] += city_result['contacts_processed']
                    self.stats['accommodations_updated'] += city_result['accommodations_updated']
                    self.stats['locations_updated'] += city_result['locations_updated']
                    self.stats['phones_added'] += city_result['phones_added']
                    self.stats['websites_added'] += city_result['websites_added']
                    self.stats['addresses_added'] += city_result['addresses_added']

                self.stats['errors'].extend(city_result['errors'])

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("\n" + "="*80)
            logger.info("[SUCCESS] CONTACT DATA LOADING COMPLETED")
            logger.info("="*80)
            logger.info(f"[TIME] Duration: {duration}")
            logger.info(f"[CITIES] Cities: {self.stats['cities_processed']}/{len(target_cities)}")
            logger.info(f"[CONTACTS] Contacts: {self.stats['contacts_processed']}")
            logger.info(f"[ACCOMMODATIONS] Accommodations Updated: {self.stats['accommodations_updated']}")
            logger.info(f"[LOCATIONS] Locations Updated: {self.stats['locations_updated']}")
            logger.info(f"[PHONES] Phones Added: {self.stats['phones_added']}")
            logger.info(f"[WEBSITES] Websites Added: {self.stats['websites_added']}")
            logger.info(f"[ADDRESSES] Addresses Added: {self.stats['addresses_added']}")
            logger.info(f"[ERRORS] Errors: {len(self.stats['errors'])}")

            results['overall'] = self.stats
            return results

        except Exception as e:
            logger.error(f"❌ Contact data loading failed: {e}")
            logger.error(traceback.format_exc())
            results['overall']['errors'].append(str(e))
            return results
