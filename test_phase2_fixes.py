#!/usr/bin/env python3
"""
Test script to verify Phase 2 fixes work correctly
Tests amenity extraction and city mapping without database connection
"""

import json
import sys
from pathlib import Path
from typing import Set, Tuple

# Import the amenity normalizer from the main script
sys.path.append('.')
from phase2_reference_data_loader import AmenityNormalizer

def test_amenity_extraction(data_directory: str) -> Set[Tuple[str, str]]:
    """Test amenity extraction from JSON files"""
    print("Testing amenity extraction...")
    
    all_amenities = set()
    amenity_normalizer = AmenityNormalizer()
    data_dir = Path(data_directory)
    
    # Process amenities from amenities directory
    amenities_dir = data_dir / 'amenities'
    if amenities_dir.exists():
        file_count = 0
        for amenity_file in amenities_dir.rglob("*.json"):
            if file_count >= 5:  # Test only first 5 files
                break
                
            try:
                with open(amenity_file, 'r', encoding='utf-8') as f:
                    amenity_data = json.load(f)
                
                print(f"\nProcessing: {amenity_file.name}")
                
                # Handle the nested structure: hotel_details array
                hotel_details_list = amenity_data.get('hotel_details', [])
                print(f"  Found {len(hotel_details_list)} hotel_details entries")
                
                for hotel_details in hotel_details_list:
                    # Process grouped amenities
                    if 'amenities_by_group' in hotel_details:
                        print(f"  Found amenities_by_group with {len(hotel_details['amenities_by_group'])} categories")
                        for category, amenities in hotel_details['amenities_by_group'].items():
                            print(f"    Category '{category}': {len(amenities)} amenities")
                            for amenity_name in amenities:
                                normalized_name, normalized_category = amenity_normalizer.normalize_amenity_name(amenity_name)
                                all_amenities.add((normalized_name, normalized_category))
                    
                    # Process top amenities
                    if 'top_amenities' in hotel_details:
                        print(f"  Found top_amenities with {len(hotel_details['top_amenities'])} items")
                        for amenity_name in hotel_details['top_amenities'].keys():
                            normalized_name, normalized_category = amenity_normalizer.normalize_amenity_name(amenity_name)
                            all_amenities.add((normalized_name, normalized_category))
                
                file_count += 1
            
            except Exception as e:
                print(f"  ERROR processing {amenity_file}: {e}")
    
    print(f"\nTotal unique amenities extracted: {len(all_amenities)}")
    
    # Show sample amenities
    print("\nSample amenities (first 10):")
    for i, (name, category) in enumerate(sorted(all_amenities)):
        if i >= 10:
            break
        print(f"  {name} -> {category}")
    
    return all_amenities

def test_city_mapping(data_directory: str):
    """Test city filename mapping logic"""
    print("\n" + "="*50)
    print("Testing city filename mapping...")
    
    data_dir = Path(data_directory)
    
    # Simulate available cities (you should update this with your actual cities)
    mock_cities = {
        'casablanca': 1,
        'marrakech': 2, 
        'rabat': 3,
        'fez': 4,  # Note: fez, not fes
        'tangier': 5,
        'agadir': 6,
        'meknes': 7,
        'oujda': 8,
        'tetouan': 9
    }
    
    # Known city name variations
    city_variations = {
        'fes': 'fez',  # fes.json should map to Fez city
        'fès': 'fez',  # Alternative spelling
    }
    
    print(f"Mock cities available: {list(mock_cities.keys())}")
    
    city_mapping = {}
    
    # Map JSON files to cities
    for json_file in data_dir.glob("*.json"):
        filename = json_file.stem.lower()  # Remove .json extension and lowercase
        
        print(f"\nProcessing: {json_file.name} (filename: {filename})")
        
        # Check for exact match
        if filename in mock_cities:
            city_mapping[json_file.name] = mock_cities[filename]
            print(f"  ✓ Exact match: {filename} -> city ID {mock_cities[filename]}")
        # Check for known variations
        elif filename in city_variations and city_variations[filename] in mock_cities:
            mapped_city = city_variations[filename]
            city_mapping[json_file.name] = mock_cities[mapped_city]
            print(f"  ✓ Variation match: {filename} -> {mapped_city} -> city ID {mock_cities[mapped_city]}")
        else:
            # Try partial matching
            matched = False
            for city_name, city_id in mock_cities.items():
                if filename in city_name or city_name in filename:
                    city_mapping[json_file.name] = city_id
                    print(f"  ✓ Partial match: {filename} <-> {city_name} -> city ID {city_id}")
                    matched = True
                    break
            
            if not matched:
                # Skip files that don't have corresponding cities (like nador)
                if filename not in ['nador']:  # Known files to skip
                    print(f"  ✗ No mapping found for {json_file.name}")
                else:
                    print(f"  - Skipping {json_file.name} - city not in database (expected)")
    
    print(f"\nTotal files mapped: {len(city_mapping)}")
    print("Mappings created:")
    for filename, city_id in city_mapping.items():
        print(f"  {filename} -> city ID {city_id}")

def main():
    """Main test function"""
    
    # Data directory path - UPDATE THIS PATH
    DATA_DIRECTORY = "processed_data"
    
    if not Path(DATA_DIRECTORY).exists():
        print(f"ERROR: Data directory '{DATA_DIRECTORY}' does not exist!")
        print("Please update the DATA_DIRECTORY variable in this script.")
        sys.exit(1)
    
    print("="*60)
    print("PHASE 2 FIXES VERIFICATION TEST")
    print("="*60)
    
    # Test amenity extraction
    amenities = test_amenity_extraction(DATA_DIRECTORY)
    
    # Test city mapping
    test_city_mapping(DATA_DIRECTORY)
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"✓ Amenity extraction: {len(amenities)} unique amenities found")
    print("✓ City mapping: Logic working correctly")
    print("\nIf you see amenities extracted and city mappings created,")
    print("the fixes should resolve your Phase 2 issues!")

if __name__ == "__main__":
    main()
