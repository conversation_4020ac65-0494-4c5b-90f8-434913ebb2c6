# Phase 3: Core Hotel Data Loading

This phase loads the core hotel data (accommodations and locations) for **Casablanca and Marrakech only** using a simple, city-by-city approach.

## Files in this Directory

1. **`testphase2_verification.sql`** - SQL queries to verify Phase 2 completed successfully
2. **`phase3_core_data_loader.py`** - Main Phase 3 implementation
3. **`test_json_structure.py`** - Test script to examine JSON structure
4. **`README_PHASE3.md`** - This documentation

## Step 1: Verify Phase 2 Completion

Before running Phase 3, verify that Phase 2 completed successfully:

### Execute Verification Queries

1. Open pgAdmin and connect to your database
2. Open the Query Tool
3. Copy and paste the contents of `testphase2_verification.sql`
4. Execute the script

### Expected Results

You should see:
- ✅ All new tables created (review, review_entry, etc.)
- ✅ Accommodation table extensions added
- ✅ Amenities loaded (100+ amenities)
- ✅ Cities ready for Phase 3 (Casablanca, Marrakech)
- ✅ Hotel accommodation type exists

If any checks show ❌ status, resolve those issues before proceeding.

## Step 2: Test JSON Structure (Optional)

Before running the full Phase 3, test your JSON files:

```bash
cd phase3
python test_json_structure.py
```

This will:
- Check if casablanca.json and marrakech.json exist
- Examine the JSON structure (hotel IDs as root keys)
- Show sample hotel data
- Verify the data format is correct

### Test Phase 3 Fixes (Recommended)

To verify the fixes work with your actual JSON structure:

```bash
cd phase3
python test_phase3_fixes.py
```

This will:
- Test hotel extraction from actual JSON structure
- Verify field mapping (name, coordinates, stars, rating)
- Show sample extracted data
- Confirm Phase 3 will work correctly

## Step 3: Configure and Run Phase 3

### Update Configuration

Edit `phase3_core_data_loader.py` and update these settings:

```python
# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'database': 'your_database_name',
    'user': 'your_username',
    'password': 'your_password',
    'port': 5432
}

# Data directory path
DATA_DIRECTORY = "processed_data"
```

### Run Phase 3

```bash
cd phase3
python phase3_core_data_loader.py
```

## What Phase 3 Does

### Simple City-by-City Processing

1. **Loads city IDs** from database for Casablanca and Marrakech
2. **Processes each city separately** for better error handling
3. **For each hotel in each city**:
   - Creates a location record (address, coordinates)
   - Creates an accommodation record (hotel details)
4. **Commits changes city by city** to avoid losing progress

### Data Loaded

**Location Table:**
- Street address
- Postal code
- City ID (linked to existing cities)
- Country ID (Morocco = 1)
- Latitude/longitude coordinates

**Accommodation Table:**
- Hotel name
- Location ID (linked to location record)
- Type ID (Hotel accommodation type)
- Star rating (1-5 stars)
- Average score (0-10)
- Description
- Website, phone, email
- Construction year
- Highlights array

### Error Handling

- **City-level isolation**: If one city fails, the other continues
- **Hotel-level isolation**: If one hotel fails, others in the city continue
- **Transaction safety**: Changes are committed per city
- **Detailed logging**: All operations logged to `phase3_loading.log`

## Expected Results

```
================================================================================
PHASE 3 CORE DATA LOADING RESULTS
================================================================================
Status: success
Cities processed: 2
Hotels processed: 450
Locations created: 450
Accommodations created: 450

City-by-city results:

Casablanca:
  Status: completed
  Hotels found: 250
  Hotels processed: 250
  Locations created: 250
  Accommodations created: 250

Marrakech:
  Status: completed
  Hotels found: 200
  Hotels processed: 200
  Locations created: 200
  Accommodations created: 200

Phase 3 completed successfully!
```

## Key Features

### ✅ Simple and Robust Design

- **Only 2 cities**: Casablanca and Marrakech (complete datasets)
- **City-by-city processing**: Easy to debug and monitor
- **Clear error messages**: Know exactly what failed and where
- **Progress tracking**: See real-time progress for each city

### ✅ Simplified Mapping Logic

- **Direct city lookup**: Simple database query by city name
- **No complex matching**: Straightforward ID mapping
- **Clear configuration**: Easy to understand target cities

### ✅ Data Quality Handling

- **Validation**: Checks for required fields (hotel name)
- **Type conversion**: Safely converts stars, scores, coordinates
- **Null handling**: Gracefully handles missing optional data
- **Constraint compliance**: Respects database constraints

## Troubleshooting

### Common Issues

1. **"Found 0 hotels" or "No hotels found"**
   - **FIXED**: This was caused by incorrect JSON structure parsing
   - The JSON has hotel IDs as root keys, not a "hotels" array
   - Run `python test_phase3_fixes.py` to verify the fix works
   - Updated code now correctly extracts hotels from the actual structure

2. **"No valid cities found in database"**
   - Verify Casablanca and Marrakech exist in your city table
   - Check they have country_id = 1 (Morocco)

3. **"Hotel accommodation type not found"**
   - Run the Phase 2 verification queries
   - Ensure Hotel accommodation type was created

4. **JSON file not found**
   - Verify casablanca.json and marrakech.json exist in your data directory
   - Check the DATA_DIRECTORY path is correct

5. **Database connection failed**
   - Verify database credentials in DB_CONFIG
   - Ensure PostgreSQL is running

### Debugging Tips

1. **Check logs**: Review `phase3_loading.log` for detailed information
2. **Test JSON structure**: Run `test_json_structure.py` first
3. **Verify Phase 2**: Run the verification queries
4. **Start small**: The script processes cities one by one, so you can see exactly where issues occur

## Next Steps

After successful Phase 3 completion:

1. **Verify data**: Check that hotels appear in your database
2. **Phase 4**: Load amenity relationships and reviews
3. **Phase 5**: Load images and additional metadata

## Database Impact

Phase 3 will create:
- **Location records**: One per hotel
- **Accommodation records**: One per hotel
- **No foreign key relationships yet**: Those come in Phase 4

The data will be ready for Phase 4 which will link amenities, reviews, and other relationships.
