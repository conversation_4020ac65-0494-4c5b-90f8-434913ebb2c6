-- =====================================================
-- V5 SCHEMA MODIFICATIONS FOR COMPREHENSIVE DATA LOADING
-- Execute these statements in the exact order provided
-- =====================================================

-- =====================================================
-- STEP 1: CREATE NEW SEQUENCES
-- =====================================================

CREATE SEQUENCE public.review_id_seq
    AS bigint
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.review_id_seq OWNER TO postgres;

CREATE SEQUENCE public.review_entry_id_seq
    AS bigint
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.review_entry_id_seq OWNER TO postgres;

CREATE SEQUENCE public.review_aspect_rating_id_seq
    AS bigint
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.review_aspect_rating_id_seq OWNER TO postgres;

CREATE SEQUENCE public.accommodation_price_forecast_id_seq
    AS bigint
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.accommodation_price_forecast_id_seq OWNER TO postgres;

-- =====================================================
-- STEP 2: CREATE REVIEW SYSTEM TABLES
-- =====================================================

-- Main review aggregation table
CREATE TABLE public.review (
    id bigint NOT NULL DEFAULT nextval('review_id_seq'::regclass),
    entity_type character varying(20) NOT NULL,
    entity_id bigint NOT NULL,
    overall_rating integer,
    formatted_rating character varying(10),
    review_count integer,
    language_tag character varying(10),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT review_entity_type_check CHECK (((entity_type)::text = ANY (ARRAY[('accommodation'::character varying)::text, ('restaurant'::character varying)::text, ('activity'::character varying)::text]))),
    CONSTRAINT review_overall_rating_check CHECK (((overall_rating >= 0) AND (overall_rating <= 10000)))
);

ALTER TABLE public.review OWNER TO postgres;
ALTER SEQUENCE public.review_id_seq OWNED BY public.review.id;

-- Individual review entries
CREATE TABLE public.review_entry (
    id bigint NOT NULL DEFAULT nextval('review_entry_id_seq'::regclass),
    review_id bigint NOT NULL,
    review_text text,
    rating integer,
    author character varying(200),
    travelled_at timestamp with time zone,
    created_at timestamp with time zone,
    advertiser character varying(100),
    CONSTRAINT review_entry_rating_check CHECK (((rating >= 0) AND (rating <= 10000)))
);

ALTER TABLE public.review_entry OWNER TO postgres;
ALTER SEQUENCE public.review_entry_id_seq OWNED BY public.review_entry.id;

-- Aspect ratings (cleanliness, comfort, etc.)
CREATE TABLE public.review_aspect_rating (
    id bigint NOT NULL DEFAULT nextval('review_aspect_rating_id_seq'::regclass),
    review_id bigint NOT NULL,
    aspect_type character varying(50) NOT NULL,
    rating_value integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT review_aspect_rating_value_check CHECK (((rating_value >= 0) AND (rating_value <= 10000)))
);

ALTER TABLE public.review_aspect_rating OWNER TO postgres;
ALTER SEQUENCE public.review_aspect_rating_id_seq OWNED BY public.review_aspect_rating.id;

-- =====================================================
-- STEP 3: CREATE PRICE FORECAST TABLE
-- =====================================================

CREATE TABLE public.accommodation_price_forecast (
    id bigint NOT NULL DEFAULT nextval('accommodation_price_forecast_id_seq'::regclass),
    accommodation_id bigint NOT NULL,
    forecast_month character varying(7) NOT NULL,
    price_value numeric(12,2) NOT NULL,
    currency character(3) DEFAULT 'MAD'::bpchar NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);

ALTER TABLE public.accommodation_price_forecast OWNER TO postgres;
ALTER SEQUENCE public.accommodation_price_forecast_id_seq OWNED BY public.accommodation_price_forecast.id;

-- =====================================================
-- STEP 4: ADD PRIMARY KEY CONSTRAINTS
-- =====================================================

ALTER TABLE ONLY public.review
    ADD CONSTRAINT review_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.review_entry
    ADD CONSTRAINT review_entry_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.review_aspect_rating
    ADD CONSTRAINT review_aspect_rating_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT accommodation_price_forecast_pkey PRIMARY KEY (id);

-- =====================================================
-- STEP 5: ADD UNIQUE CONSTRAINTS
-- =====================================================

-- Ensure unique price forecasts per accommodation per month
ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT accommodation_price_forecast_unique UNIQUE (accommodation_id, forecast_month);

-- Ensure unique reviews per entity
ALTER TABLE ONLY public.review
    ADD CONSTRAINT review_entity_unique UNIQUE (entity_type, entity_id);

-- =====================================================
-- STEP 6: MODIFY EXISTING ACCOMMODATION TABLE
-- =====================================================

-- Add new columns for construction year and highlights (separate statements)
ALTER TABLE public.accommodation ADD COLUMN construction_year integer;
ALTER TABLE public.accommodation ADD COLUMN highlights text[];

-- Extend phone field to handle international numbers
ALTER TABLE public.accommodation ALTER COLUMN phone TYPE character varying(50);

-- Temporarily drop stars constraint to handle null values
ALTER TABLE public.accommodation DROP CONSTRAINT IF EXISTS accommodation_stars_check;

-- Add new stars constraint that allows null values
ALTER TABLE public.accommodation ADD CONSTRAINT accommodation_stars_check CHECK (stars IS NULL OR (stars >= 1 AND stars <= 5));

-- =====================================================
-- STEP 7: MODIFY AMENITY JUNCTION TABLE
-- =====================================================

-- Add the exact columns specified for amenity pricing/availability (separate statements)
ALTER TABLE public.accommodation_amenity_junction ADD COLUMN is_free BOOLEAN;
ALTER TABLE public.accommodation_amenity_junction ADD COLUMN is_available BOOLEAN;
ALTER TABLE public.accommodation_amenity_junction ADD COLUMN is_top_amenity BOOLEAN DEFAULT FALSE;

-- =====================================================
-- STEP 8: ADD FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Review system foreign keys
ALTER TABLE ONLY public.review_entry
    ADD CONSTRAINT fk_review_entry_review FOREIGN KEY (review_id) REFERENCES public.review(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.review_aspect_rating
    ADD CONSTRAINT fk_review_aspect_rating_review FOREIGN KEY (review_id) REFERENCES public.review(id) ON DELETE CASCADE;

-- Price forecast foreign key
ALTER TABLE ONLY public.accommodation_price_forecast
    ADD CONSTRAINT fk_accommodation_price_forecast_accommodation FOREIGN KEY (accommodation_id) REFERENCES public.accommodation(id) ON DELETE CASCADE;

-- =====================================================
-- STEP 9: CREATE PERFORMANCE INDEXES
-- =====================================================

-- Review system indexes
CREATE INDEX idx_review_entity ON public.review USING btree (entity_type, entity_id);
CREATE INDEX idx_review_entry_review_id ON public.review_entry USING btree (review_id);
CREATE INDEX idx_review_aspect_rating_review_id ON public.review_aspect_rating USING btree (review_id);

-- Price forecast indexes
CREATE INDEX idx_accommodation_price_forecast_accommodation_id ON public.accommodation_price_forecast USING btree (accommodation_id);
CREATE INDEX idx_accommodation_price_forecast_month ON public.accommodation_price_forecast USING btree (forecast_month);

-- Amenity junction indexes for new columns
CREATE INDEX idx_accommodation_amenity_junction_top ON public.accommodation_amenity_junction USING btree (accommodation_id, is_top_amenity) WHERE (is_top_amenity = true);
CREATE INDEX idx_accommodation_amenity_junction_free ON public.accommodation_amenity_junction USING btree (accommodation_id, is_free) WHERE (is_free = true);

-- =====================================================
-- STEP 10: CREATE TEMPORARY TABLES FOR DATA LOADING
-- =====================================================

-- Temporary table for external ID mapping during load
CREATE TEMPORARY TABLE temp_hotel_id_mapping (
    external_hotel_id bigint NOT NULL,
    accommodation_id bigint NOT NULL,
    city_name character varying(100) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    PRIMARY KEY (external_hotel_id, city_name)
);

-- Temporary table for city-filename mapping
CREATE TEMPORARY TABLE temp_city_file_mapping (
    city_id bigint NOT NULL,
    city_name character varying(100) NOT NULL,
    filename character varying(100) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    PRIMARY KEY (filename)
);

-- =====================================================
-- SCHEMA MODIFICATIONS COMPLETE
-- =====================================================

-- Verify all modifications were applied successfully
SELECT 'Schema modifications completed successfully' AS status;
