# Phase 4: Amenity and Review Relationships Data Loader

## Overview

Phase 4 loads amenity and review data from processed JSON files and establishes relationships with accommodations using the external ID system established in Phase 3. This phase completes the core accommodation data by adding detailed amenities and comprehensive review information.

## Features

### Amenity Loading
- **Amenity Categories**: Processes amenities grouped by categories (Property amenities, Room amenities, Accessibility, etc.)
- **Top Amenities**: Handles special "top amenities" with additional metadata (is_free, is_available)
- **Deduplication**: Prevents duplicate amenity mappings for the same accommodation
- **Category Management**: Automatically creates amenity categories and maintains relationships

### Review Loading
- **Summary Reviews**: Overall ratings and review counts from Trivago
- **Individual Reviews**: Detailed user reviews with content, ratings, and metadata
- **Aspect Ratings**: Specific aspect scores (Cleanliness, Service, Location, etc.)
- **Advertiser Ratings**: Platform-specific ratings (Expedia, Hotels.com, etc.)
- **Date Handling**: Proper parsing of travel dates and review creation dates

### Data Integrity
- **External ID Linking**: Uses external IDs from Phase 3 to link amenities and reviews to correct accommodations
- **Transaction Management**: Individual hotel-level transactions for robust error handling
- **Comprehensive Logging**: Detailed progress tracking and error reporting
- **Validation Support**: Extensive SQL queries for data verification

## File Structure

```
phase4/
├── phase4_amenity_review_loader.py    # Main data loader script
├── phase4_validation_queries.sql      # Comprehensive validation queries
└── README.md                          # This documentation
```

## Data Sources

### Input Files
- **Hotel Data**: `processed_data/{city}.json` - Main hotel data with external IDs
- **Amenity Data**: `processed_data/amenities/{city}/{hotel_id}.json` - Hotel amenities by category
- **Review Data**: `processed_data/reviews/{city}/{hotel_id}.json` - Review ratings and content

### Database Tables
- **amenity**: Stores unique amenities with categories
- **accommodation_amenity_junction**: Many-to-many relationship between accommodations and amenities
- **review**: Stores all types of reviews (summary, individual, aspect, advertiser)

## Usage

### Running the Loader

```bash
# Navigate to phase4 directory
cd phase4

# Run the amenity and review loader
python phase4_amenity_review_loader.py
```

### Validation

```bash
# Run validation queries in PostgreSQL
psql -d travel_data -f phase4_validation_queries.sql
```

## Data Processing Logic

### Amenity Processing
1. **Load Amenity Data**: Read amenity JSON files for each hotel
2. **Parse Categories**: Extract amenities grouped by categories
3. **Create Amenities**: Get or create amenity records in the database
4. **Map to Accommodations**: Create accommodation-amenity relationships
5. **Handle Top Amenities**: Process special amenities with additional metadata

### Review Processing
1. **Load Review Data**: Read review JSON files for each hotel
2. **Summary Reviews**: Create overall rating summaries
3. **Individual Reviews**: Process detailed user reviews
4. **Aspect Ratings**: Store specific aspect scores
5. **Advertiser Ratings**: Handle platform-specific ratings

### Error Handling
- **Hotel-Level Transactions**: Each hotel is processed in its own transaction
- **Graceful Degradation**: Continues processing other hotels if one fails
- **Comprehensive Logging**: Detailed error tracking and progress reporting
- **Rollback Safety**: Failed hotels are rolled back without affecting others

## Expected Results

### Amenity Data
- **Categories**: Property amenities, Room amenities, Accessibility, Food and beverage, For children, Top amenities
- **Coverage**: High percentage of accommodations should have amenity mappings
- **Relationships**: Many-to-many relationships between accommodations and amenities

### Review Data
- **Summary Reviews**: One per accommodation with overall rating and count
- **Individual Reviews**: Multiple detailed reviews per accommodation
- **Aspect Ratings**: Specific scores for different aspects (Cleanliness, Service, etc.)
- **Advertiser Ratings**: Platform-specific ratings from various booking sites

## Validation Queries

The validation SQL file includes 17 comprehensive queries:

1. **Amenity Statistics**: Overall amenity counts and coverage
2. **Category Breakdown**: Amenities by category
3. **Top Amenities**: Most common amenities analysis
4. **City Coverage**: Amenity coverage by city
5. **Sample Data**: Examples of accommodations with amenities
6. **Review Statistics**: Overall review counts and types
7. **Review Types**: Breakdown by review type
8. **Review Coverage**: Review coverage by city
9. **Review Sources**: Analysis by platform/source
10. **Sample Reviews**: Examples of accommodations with reviews
11. **Aspect Analysis**: Review aspects breakdown
12. **Individual Reviews**: Detailed review content analysis
13. **External ID Check**: Consistency validation
14. **Mapping Integrity**: Relationship validation
15. **Review Integrity**: Review-accommodation links
16. **Loading Summary**: Overall Phase 4 results
17. **Quality Score**: Data quality assessment

## Success Criteria

### Amenity Loading
- ✅ All amenity categories properly loaded
- ✅ Accommodation-amenity relationships established
- ✅ Top amenities with metadata correctly processed
- ✅ No duplicate amenity mappings

### Review Loading
- ✅ Summary reviews created for accommodations
- ✅ Individual reviews with content loaded
- ✅ Aspect ratings properly categorized
- ✅ Advertiser ratings from multiple platforms

### Data Quality
- ✅ High coverage percentage (>80%) for both amenities and reviews
- ✅ All external ID relationships maintained
- ✅ No orphaned records or broken relationships
- ✅ Consistent data across cities

## Troubleshooting

### Common Issues

1. **Missing Amenity/Review Files**
   - Check if processed_data directory structure is correct
   - Verify amenities and reviews subdirectories exist

2. **External ID Mismatches**
   - Ensure Phase 3 completed successfully
   - Verify external_id field is populated in accommodation table

3. **Database Connection Issues**
   - Check PostgreSQL service is running
   - Verify database credentials in the script

4. **Memory Issues with Large Datasets**
   - Monitor system resources during processing
   - Consider processing cities individually if needed

### Log Analysis
- Check `phase4_amenity_review_loader.log` for detailed execution logs
- Look for patterns in error messages
- Monitor progress indicators for stuck processes

## Integration with Other Phases

### Dependencies
- **Phase 1**: Database schema and basic structure
- **Phase 2**: Location and city data
- **Phase 3**: Accommodation data with external IDs

### Prepares for
- **Phase 5**: Contact and booking information loading
- **Future Phases**: Advanced analytics and reporting

## Performance Considerations

- **Batch Processing**: Hotels processed individually with commits
- **Memory Management**: Efficient JSON parsing and database operations
- **Progress Tracking**: Regular progress updates every 50 hotels
- **Error Isolation**: Failed hotels don't affect others

## Data Schema Integration

This phase populates:
- `amenity` table with unique amenities and categories
- `accommodation_amenity_junction` table with many-to-many relationships
- `review` table with all types of review data

The external ID system from Phase 3 ensures proper linking between accommodations and their amenities/reviews.
