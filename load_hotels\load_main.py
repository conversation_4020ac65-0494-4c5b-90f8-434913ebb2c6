#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Main Orchestrator
Single command execution for all hotel data loading phases.

Usage: python load_main.py

This script orchestrates all phases:
1. Core Data Loading (hotels, locations, price forecasts, images)
2. Amenities & Reviews Loading
3. Contact Information Loading

Features:
- Centralized configuration
- Comprehensive error handling
- Rollback mechanisms
- Progress reporting
- Easy city configuration
"""

import sys
import os
import logging
import traceback
from datetime import datetime
from typing import Dict, List

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import HotelLoadingConfig
from database import DatabaseManager
from core_data_loader import CoreDataLoader
from amenities_reviews_loader import AmenitiesReviewsLoader
from contacts_loader import ContactsLoader
from utils import LoggingSetup, StatisticsCollector

class HotelDataOrchestrator:
    """Main orchestrator for hotel data loading system"""

    def __init__(self):
        self.config = HotelLoadingConfig()
        self.db_manager = None
        self.stats_collector = StatisticsCollector()
        self.logger = None

        # Phase loaders
        self.core_loader = None
        self.amenities_reviews_loader = None
        self.contacts_loader = None

    def setup_logging(self):
        """Setup logging configuration"""
        log_file = f"hotel_loading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.logger = LoggingSetup.setup_logging(log_file, self.config.LOG_LEVEL)
        return self.logger

    def validate_prerequisites(self) -> bool:
        """Validate all prerequisites before starting"""
        logger = logging.getLogger(__name__)

        logger.info("🔍 VALIDATING PREREQUISITES")
        logger.info("="*60)

        # Test database connection
        logger.info("Testing database connection...")
        if not self.db_manager.test_connection():
            logger.error("❌ Database connection test failed")
            return False

        # Validate data files
        logger.info("Validating data files...")
        validation_results = self.config.validate_data_files()

        all_files_valid = True
        for city_key, city_results in validation_results.items():
            city_config = self.config.get_city_config(city_key)
            logger.info(f"  {city_config['city_name']}:")

            for data_type, exists in city_results.items():
                status = "✅" if exists else "❌"
                logger.info(f"    {status} {data_type}")
                if not exists:
                    all_files_valid = False

        if not all_files_valid:
            logger.error("❌ Some required data files are missing")
            return False

        # Load and validate city IDs
        logger.info("Loading city IDs from database...")
        try:
            self.target_cities = self.db_manager.load_city_ids(self.config.TARGET_CITIES)
            logger.info("✅ All city IDs loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load city IDs: {e}")
            return False

        # Get initial database statistics
        logger.info("Getting initial database statistics...")
        initial_stats = self.db_manager.get_database_stats()
        logger.info(f"  Current accommodations: {initial_stats.get('total_accommodations', 0)}")
        logger.info(f"  With external_id: {initial_stats.get('accommodations_with_external_id', 0)}")
        logger.info(f"  With phone: {initial_stats.get('with_phone', 0)}")
        logger.info(f"  With website: {initial_stats.get('with_website', 0)}")
        logger.info(f"  With address: {initial_stats.get('with_address', 0)}")
        logger.info(f"  Amenity mappings: {initial_stats.get('amenity_mappings', 0)}")
        logger.info(f"  Reviews: {initial_stats.get('reviews', 0)}")

        logger.info("✅ All prerequisites validated successfully")
        return True

    def initialize_loaders(self):
        """Initialize all phase loaders"""
        self.core_loader = CoreDataLoader(self.db_manager)
        self.amenities_reviews_loader = AmenitiesReviewsLoader(self.db_manager)
        self.contacts_loader = ContactsLoader(self.db_manager)

    def run_phase_1_core_data(self) -> bool:
        """Run Phase 1: Core Data Loading"""
        logger = logging.getLogger(__name__)

        if not self.config.PHASES['core_data']['enabled']:
            logger.info("⏭️  Phase 1 (Core Data) is disabled, skipping...")
            return True

        try:
            self.stats_collector.start_phase('core_data')
            logger.info("\n" + "🚀 PHASE 1: CORE DATA LOADING")
            logger.info("="*80)

            results = self.core_loader.load_core_data(self.target_cities)

            # Update statistics
            for city_key, city_result in results['cities'].items():
                self.stats_collector.update_city_stats('core_data', city_key, city_result)

            success = all(
                city_result['status'] == 'completed'
                for city_result in results['cities'].values()
            )

            self.stats_collector.complete_phase('core_data', success)

            if success:
                logger.info("✅ Phase 1 (Core Data) completed successfully")
            else:
                logger.error("❌ Phase 1 (Core Data) completed with errors")

            return success

        except Exception as e:
            logger.error(f"❌ Phase 1 (Core Data) failed: {e}")
            logger.error(traceback.format_exc())
            self.stats_collector.complete_phase('core_data', False)
            return False

    def run_phase_2_amenities_reviews(self) -> bool:
        """Run Phase 2: Amenities & Reviews Loading"""
        logger = logging.getLogger(__name__)

        if not self.config.PHASES['amenities_reviews']['enabled']:
            logger.info("⏭️  Phase 2 (Amenities & Reviews) is disabled, skipping...")
            return True

        try:
            self.stats_collector.start_phase('amenities_reviews')
            logger.info("\n" + "🚀 PHASE 2: AMENITIES & REVIEWS LOADING")
            logger.info("="*80)

            results = self.amenities_reviews_loader.load_amenities_reviews(self.target_cities)

            # Update statistics
            for city_key, city_result in results['cities'].items():
                self.stats_collector.update_city_stats('amenities_reviews', city_key, city_result)

            success = all(
                city_result['status'] == 'completed'
                for city_result in results['cities'].values()
            )

            self.stats_collector.complete_phase('amenities_reviews', success)

            if success:
                logger.info("✅ Phase 2 (Amenities & Reviews) completed successfully")
            else:
                logger.error("❌ Phase 2 (Amenities & Reviews) completed with errors")

            return success

        except Exception as e:
            logger.error(f"❌ Phase 2 (Amenities & Reviews) failed: {e}")
            logger.error(traceback.format_exc())
            self.stats_collector.complete_phase('amenities_reviews', False)
            return False

    def run_phase_3_contacts(self) -> bool:
        """Run Phase 3: Contact Information Loading"""
        logger = logging.getLogger(__name__)

        if not self.config.PHASES['contacts']['enabled']:
            logger.info("⏭️  Phase 3 (Contacts) is disabled, skipping...")
            return True

        try:
            self.stats_collector.start_phase('contacts')
            logger.info("\n" + "🚀 PHASE 3: CONTACT INFORMATION LOADING")
            logger.info("="*80)

            results = self.contacts_loader.load_contacts(self.target_cities)

            # Update statistics
            for city_key, city_result in results['cities'].items():
                self.stats_collector.update_city_stats('contacts', city_key, city_result)

            success = all(
                city_result['status'] == 'completed'
                for city_result in results['cities'].values()
            )

            self.stats_collector.complete_phase('contacts', success)

            if success:
                logger.info("[OK] Phase 3 (Contacts) completed successfully")
            else:
                logger.error("[ERROR] Phase 3 (Contacts) completed with errors")

            return success

        except Exception as e:
            logger.error(f"[ERROR] Phase 3 (Contacts) failed: {e}")
            logger.error(traceback.format_exc())
            self.stats_collector.complete_phase('contacts', False)
            return False

    def print_final_summary(self, overall_success: bool):
        """Print final summary of all operations"""
        logger = logging.getLogger(__name__)

        summary = self.stats_collector.get_summary()

        logger.info("\n" + "="*80)
        logger.info("[SUCCESS] HOTEL DATA LOADING COMPLETED")
        logger.info("="*80)

        # Overall statistics
        logger.info(f"[TIME] Total Duration: {summary['overall']['total_duration']}")
        logger.info(f"[STATS] Overall Success Rate: {summary['overall']['success_rate']}%")
        logger.info(f"[ERRORS] Total Errors: {summary['overall']['total_errors']}")

        # Phase-by-phase summary
        logger.info("\n[PHASES] PHASE SUMMARY:")
        for phase_name, phase_stats in summary['phases'].items():
            status_text = "[OK]" if phase_stats['status'] == 'completed' else "[ERROR]"
            duration = phase_stats.get('duration', 'N/A')
            logger.info(f"  {status_text} {phase_name.replace('_', ' ').title()}: {phase_stats['status']} ({duration})")

        # City-by-city summary
        logger.info("\n[CITIES] CITY SUMMARY:")
        for city_key in self.target_cities.keys():
            city_config = self.config.get_city_config(city_key)
            logger.info(f"  [CITY] {city_config['city_name']}:")

            for phase_name, phase_stats in summary['phases'].items():
                if city_key in phase_stats.get('cities', {}):
                    city_result = phase_stats['cities'][city_key]
                    status_text = "[OK]" if city_result['status'] == 'completed' else "[ERROR]"
                    logger.info(f"    {status_text} {phase_name.replace('_', ' ').title()}: {city_result['status']}")

        # Final database statistics
        logger.info("\n[DATABASE] FINAL DATABASE STATISTICS:")
        final_stats = self.db_manager.get_database_stats()
        logger.info(f"  Total accommodations: {final_stats.get('total_accommodations', 0)}")
        logger.info(f"  With external_id: {final_stats.get('accommodations_with_external_id', 0)}")
        logger.info(f"  With phone: {final_stats.get('with_phone', 0)}")
        logger.info(f"  With website: {final_stats.get('with_website', 0)}")
        logger.info(f"  With address: {final_stats.get('with_address', 0)}")
        logger.info(f"  Amenity mappings: {final_stats.get('amenity_mappings', 0)}")
        logger.info(f"  Reviews: {final_stats.get('reviews', 0)}")

        if overall_success:
            logger.info("\n[SUCCESS] ALL PHASES COMPLETED SUCCESSFULLY!")
            logger.info("[INFO] Hotel data loading system has finished successfully.")
        else:
            logger.info("\n[WARNING] SOME PHASES COMPLETED WITH ERRORS")
            logger.info("[INFO] Please review the logs for detailed error information.")

        logger.info("="*80)

    def run(self) -> bool:
        """Main execution method"""
        logger = self.setup_logging()

        try:
            logger.info("[START] CONSOLIDATED HOTEL DATA LOADING SYSTEM")
            logger.info("="*80)
            logger.info(f"Start Time: {datetime.now()}")
            logger.info(f"Target Cities: {', '.join([config['city_name'] for config in self.config.TARGET_CITIES.values()])}")
            logger.info("="*80)

            # Initialize database manager
            self.db_manager = DatabaseManager(self.config.DATABASE_CONFIG)

            # Validate prerequisites
            if not self.validate_prerequisites():
                logger.error("[ERROR] Prerequisites validation failed")
                return False

            # Initialize loaders
            self.initialize_loaders()

            # Track overall success
            overall_success = True

            # Run Phase 1: Core Data Loading
            phase1_success = self.run_phase_1_core_data()
            overall_success = overall_success and phase1_success

            # Run Phase 2: Amenities & Reviews Loading
            # Only run if Phase 1 succeeded (need accommodations with external_id)
            if phase1_success:
                phase2_success = self.run_phase_2_amenities_reviews()
                overall_success = overall_success and phase2_success
            else:
                logger.warning("[WARNING] Skipping Phase 2 due to Phase 1 failure")

            # Run Phase 3: Contact Information Loading
            # Only run if Phase 1 succeeded (need accommodations with external_id)
            if phase1_success:
                phase3_success = self.run_phase_3_contacts()
                overall_success = overall_success and phase3_success
            else:
                logger.warning("[WARNING] Skipping Phase 3 due to Phase 1 failure")

            # Print final summary
            self.print_final_summary(overall_success)

            return overall_success

        except Exception as e:
            logger.error(f"[ERROR] Hotel data loading system failed: {e}")
            logger.error(traceback.format_exc())
            return False

        finally:
            # Clean up database connection
            if self.db_manager:
                self.db_manager.close()

def main():
    """Main function"""
    try:
        orchestrator = HotelDataOrchestrator()
        success = orchestrator.run()

        # Exit with appropriate code
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n[WARNING] Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()