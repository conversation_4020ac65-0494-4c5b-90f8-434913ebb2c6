-- =====================================================
-- LOCATION CONSTRAINT ANALYSIS AND OPTIONAL FIXES
-- Execute these queries to understand and optionally fix the unique constraint issue
-- =====================================================

-- QUERY 1: Check current location constraints
SELECT 'Current Location Constraints' as analysis_type;
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'location'::regclass
ORDER BY conname;

-- QUERY 2: Check for duplicate coordinates in your data
SELECT 'Duplicate Coordinates Analysis' as analysis_type;
SELECT 
    lat, lng, 
    COUNT(*) as hotel_count,
    array_agg(name) as hotel_names
FROM location 
WHERE type = 'hotel'
GROUP BY lat, lng 
HAVING COUNT(*) > 1
ORDER BY hotel_count DESC;

-- QUERY 3: Count hotels by coordinate precision
SELECT 'Coordinate Precision Analysis' as analysis_type;
SELECT 
    'Total hotels with coordinates' as metric,
    COUNT(*) as count
FROM location 
WHERE lat IS NOT NULL AND lng IS NOT NULL AND type = 'hotel'
UNION ALL
SELECT 
    'Hotels without coordinates' as metric,
    COUNT(*) as count
FROM location 
WHERE (lat IS NULL OR lng IS NULL) AND type = 'hotel'
UNION ALL
SELECT 
    'Unique coordinate pairs' as metric,
    COUNT(DISTINCT (lat, lng)) as count
FROM location 
WHERE lat IS NOT NULL AND lng IS NOT NULL AND type = 'hotel';

-- =====================================================
-- OPTIONAL FIXES (CHOOSE ONE APPROACH)
-- =====================================================

-- APPROACH 1: Remove the unique constraint entirely (RECOMMENDED)
-- This allows multiple hotels to share the same coordinates
-- Uncomment the lines below to execute:

/*
-- Remove the unique constraint on lat, lng
ALTER TABLE location DROP CONSTRAINT IF EXISTS location_lat_lng_key;

-- Verify constraint is removed
SELECT 'Constraint Removal Verification' as verification;
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_constraint 
            WHERE conname = 'location_lat_lng_key' 
            AND conrelid = 'location'::regclass
        )
        THEN '❌ Constraint still exists'
        ELSE '✅ Constraint successfully removed'
    END as status;
*/

-- APPROACH 2: Modify constraint to allow duplicates for hotels only
-- This keeps the constraint for other location types but removes it for hotels
-- Uncomment the lines below to execute:

/*
-- Remove the existing constraint
ALTER TABLE location DROP CONSTRAINT IF EXISTS location_lat_lng_key;

-- Add a partial unique constraint that excludes hotels
CREATE UNIQUE INDEX location_lat_lng_non_hotel_unique 
ON location (lat, lng) 
WHERE type != 'hotel';

-- Verify new constraint
SELECT 'Modified Constraint Verification' as verification;
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'location' 
AND indexname = 'location_lat_lng_non_hotel_unique';
*/

-- APPROACH 3: Keep constraint but add place_id uniqueness instead
-- This approach keeps coordinate uniqueness but adds flexibility
-- Uncomment the lines below to execute:

/*
-- Remove the lat/lng constraint
ALTER TABLE location DROP CONSTRAINT IF EXISTS location_lat_lng_key;

-- Add a unique constraint on place_id (if you plan to use Google Place IDs)
-- Note: place_id can be NULL, so this allows multiple NULL values
ALTER TABLE location ADD CONSTRAINT location_place_id_unique UNIQUE (place_id);

-- Verify changes
SELECT 'Place ID Constraint Verification' as verification;
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'location'::regclass
AND conname LIKE '%place_id%';
*/

-- =====================================================
-- RECOMMENDATION
-- =====================================================

SELECT 'RECOMMENDATION' as section;
SELECT 
    'For hotel data loading, APPROACH 1 is recommended:' as recommendation,
    'Remove the unique constraint on (lat, lng) entirely.' as reason_1,
    'Multiple hotels can legitimately share coordinates.' as reason_2,
    'Examples: Hotels in same building, hotel complexes, etc.' as reason_3;

-- =====================================================
-- AFTER RUNNING THE FIX
-- =====================================================

-- After removing the constraint, you can run Phase 3 again
-- The updated code will handle coordinate conflicts gracefully:
-- 1. First tries to reuse existing locations with same coordinates
-- 2. If that fails, creates new location with small offset
-- 3. If that fails, creates location without coordinates
-- 4. Individual hotel failures won't stop the entire process

SELECT 'NEXT STEPS' as section;
SELECT 
    '1. Choose and execute one of the approaches above' as step_1,
    '2. Update your database credentials in phase3_core_data_loader.py' as step_2,
    '3. Run: python phase3_core_data_loader.py' as step_3,
    '4. All 1400+ hotels should now load successfully' as step_4;
