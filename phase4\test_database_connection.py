#!/usr/bin/env python3
"""
Phase 4 Database Connection Test
Quick test to verify database connection and schema before running the full loader.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

def test_database_connection():
    """Test database connection and basic schema"""
    print("🔍 Testing Database Connection...")

    # Database configuration - matches Phase 3 configuration
    db_config = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }

    try:
        # Test connection
        connection = psycopg2.connect(**db_config)
        connection.autocommit = False
        print("✅ Database connection successful")

        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            # Test required tables exist
            required_tables = [
                'accommodation',
                'amenity',
                'accommodation_amenity_junction',
                'review',
                'review_entry',
                'review_aspect_rating'
            ]

            print("\n🔍 Checking required tables...")
            for table in required_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    );
                """, (table,))

                exists = cursor.fetchone()['exists']
                status = "✅" if exists else "❌"
                print(f"  {status} {table}")

                if not exists:
                    print(f"    ❌ Table {table} is missing!")
                    return False

            # Test accommodation table has external_id column
            print("\n🔍 Checking accommodation table structure...")
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'accommodation'
                AND column_name = 'external_id'
            """)

            external_id_exists = cursor.fetchone() is not None
            status = "✅" if external_id_exists else "❌"
            print(f"  {status} external_id column")

            if not external_id_exists:
                print("    ❌ external_id column is missing from accommodation table!")
                return False

            # Test accommodation_amenity_junction table structure
            print("\n🔍 Checking accommodation_amenity_junction table structure...")
            required_columns = ['is_free', 'is_available', 'is_top_amenity']

            for column in required_columns:
                cursor.execute("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = 'accommodation_amenity_junction'
                    AND column_name = %s
                """, (column,))

                column_exists = cursor.fetchone() is not None
                status = "✅" if column_exists else "❌"
                print(f"  {status} {column} column")

                if not column_exists:
                    print(f"    ❌ {column} column is missing!")
                    return False

            # Test sample data exists
            print("\n🔍 Checking sample data...")

            # Check accommodations
            cursor.execute("SELECT COUNT(*) as count FROM accommodation")
            acc_count = cursor.fetchone()['count']
            print(f"  ✅ {acc_count} accommodations found")

            if acc_count == 0:
                print("    ❌ No accommodations found! Run Phase 3 first.")
                return False

            # Check accommodations with external_id
            cursor.execute("SELECT COUNT(*) as count FROM accommodation WHERE external_id IS NOT NULL")
            ext_id_count = cursor.fetchone()['count']
            print(f"  ✅ {ext_id_count} accommodations with external_id")

            if ext_id_count == 0:
                print("    ❌ No accommodations with external_id found! Run Phase 3 first.")
                return False

            # Check cities
            cursor.execute("SELECT id, name FROM city ORDER BY id")
            cities = cursor.fetchall()
            print(f"  ✅ Cities found:")
            for city in cities:
                print(f"    - {city['name']} (ID: {city['id']})")

            expected_cities = ['Casablanca', 'Marrakech']
            found_cities = [city['name'] for city in cities]

            for expected_city in expected_cities:
                if expected_city not in found_cities:
                    print(f"    ❌ Expected city {expected_city} not found!")
                    return False

        connection.close()
        print("\n🎉 All database tests passed! Ready to run Phase 4 loader.")
        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    """Main test function"""
    print("="*60)
    print("PHASE 4 DATABASE CONNECTION TEST")
    print("="*60)

    success = test_database_connection()

    if success:
        print("\n✅ Database is ready for Phase 4!")
        print("🚀 You can now run: python phase4_amenity_review_loader.py")
    else:
        print("\n❌ Database is not ready for Phase 4!")
        print("🔧 Please fix the issues above before running the loader.")

    print("="*60)

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
