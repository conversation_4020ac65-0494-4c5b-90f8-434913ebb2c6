#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - Core Data Loader
Refactored from Phase 3: Loads basic hotel information, locations, price forecasts, and images.
"""

import logging
import traceback
from typing import Dict, Optional, List
from datetime import datetime
try:
    from .database import DatabaseManager
    from .utils import FileManager, ProgressTracker, DataValidator
    from .config import HotelLoadingConfig
except ImportError:
    from database import DatabaseManager
    from utils import FileManager, ProgressTracker, DataValidator
    from config import HotelLoadingConfig

logger = logging.getLogger(__name__)

class CoreDataLoader:
    """Core hotel data loader - refactored from Phase 3"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.stats = {
            'cities_processed': 0,
            'hotels_processed': 0,
            'locations_created': 0,
            'accommodations_created': 0,
            'price_forecasts_created': 0,
            'images_created': 0,
            'errors': []
        }

    def process_single_hotel(self, hotel_id: str, hotel_data: Dict, city_id: int, accommodation_type_id: int) -> Dict:
        """Process a single hotel record"""
        result = {
            'success': False,
            'location_created': 0,
            'accommodation_created': 0,
            'price_forecasts_created': 0,
            'images_created': 0,
            'error': None
        }

        try:
            hotel_details = hotel_data.get('hotel_details', {})
            if not hotel_details:
                result['error'] = f"No hotel_details found for hotel {hotel_id}"
                return result

            # Create location
            location_id = self.create_location(hotel_details, city_id)
            if not location_id:
                result['error'] = "Failed to create location"
                return result

            result['location_created'] = 1

            # Create accommodation
            accommodation_id = self.create_accommodation(
                hotel_id, hotel_details, location_id, accommodation_type_id
            )
            if not accommodation_id:
                result['error'] = "Failed to create accommodation"
                return result

            result['accommodation_created'] = 1

            # Create price forecasts
            price_count = self.create_price_forecasts(hotel_details, accommodation_id)
            result['price_forecasts_created'] = price_count

            # Create images
            image_count = self.create_images(hotel_details, accommodation_id)
            result['images_created'] = image_count

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"    [ERROR] Hotel processing error: {e}")
            logger.error(traceback.format_exc())

        return result

    def create_location(self, hotel_details: Dict, city_id: int) -> Optional[int]:
        """Create location record"""
        try:
            with self.db.get_cursor() as cursor:
                # Extract coordinates from nested structure
                coordinates = hotel_details.get('coordinates', {})
                latitude = coordinates.get('latitude')
                longitude = coordinates.get('longitude')

                cursor.execute("""
                    INSERT INTO location (city_id, lat, lng, name, address, type, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, now(), now())
                    RETURNING id
                """, (
                    city_id,
                    latitude,
                    longitude,
                    hotel_details.get('name', f'Hotel Location'),
                    hotel_details.get('address'),
                    'hotel'  # Default type for hotel locations
                ))

                location_id = cursor.fetchone()['id']
                logger.debug(f"    [OK] Created location {location_id}")
                return location_id

        except Exception as e:
            logger.error(f"    [ERROR] Location creation error: {e}")
            return None

    def create_accommodation(self, hotel_id: str, hotel_details: Dict, location_id: int, accommodation_type_id: int) -> Optional[int]:
        """Create accommodation record"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO accommodation (
                        name, description, location_id, type_id,
                        stars, external_id, created_at, updated_at
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, now(), now())
                    RETURNING id
                """, (
                    hotel_details.get('name', f'Hotel {hotel_id}'),
                    hotel_details.get('description'),
                    location_id,
                    accommodation_type_id,
                    DataValidator.validate_rating(hotel_details.get('hotel_stars')),
                    hotel_id
                ))

                accommodation_id = cursor.fetchone()['id']
                logger.debug(f"    [OK] Created accommodation {accommodation_id}")
                return accommodation_id

        except Exception as e:
            logger.error(f"    [ERROR] Accommodation creation error: {e}")
            return None

    def create_price_forecasts(self, hotel_details: Dict, accommodation_id: int) -> int:
        """Create price forecast records"""
        try:
            price_forecasts = hotel_details.get('price_forecasts', [])
            if not price_forecasts:
                return 0

            created_count = 0
            with self.db.get_cursor() as cursor:
                for forecast in price_forecasts:
                    try:
                        cursor.execute("""
                            INSERT INTO price_forecast (
                                accommodation_id, date, price, currency, created_at, updated_at
                            )
                            VALUES (%s, %s, %s, %s, now(), now())
                        """, (
                            accommodation_id,
                            forecast.get('date'),
                            forecast.get('price'),
                            forecast.get('currency', 'MAD')
                        ))
                        created_count += 1
                    except Exception as e:
                        logger.warning(f"    [WARNING] Price forecast creation error: {e}")

            if created_count > 0:
                logger.debug(f"    [OK] Created {created_count} price forecasts")

            return created_count

        except Exception as e:
            logger.error(f"    [ERROR] Price forecasts creation error: {e}")
            return 0

    def create_images(self, hotel_details: Dict, accommodation_id: int) -> int:
        """Create image records"""
        try:
            images = hotel_details.get('images', [])
            if not images:
                return 0

            created_count = 0
            with self.db.get_cursor() as cursor:
                for image in images:
                    try:
                        cursor.execute("""
                            INSERT INTO entity_image (
                                entity_type, entity_id, image_url, alt_text, created_at, updated_at
                            )
                            VALUES (%s, %s, %s, %s, now(), now())
                        """, (
                            'accommodation',
                            accommodation_id,
                            image.get('url'),
                            image.get('alt_text', 'Hotel image')
                        ))
                        created_count += 1
                    except Exception as e:
                        logger.warning(f"    [WARNING] Image creation error: {e}")

            if created_count > 0:
                logger.debug(f"    [OK] Created {created_count} images")

            return created_count

        except Exception as e:
            logger.error(f"    [ERROR] Images creation error: {e}")
            return 0

    def process_city(self, city_key: str, city_config: Dict) -> Dict:
        """Process all hotels for a city"""
        city_stats = {
            'city_name': city_config['city_name'],
            'hotels_found': 0,
            'hotels_processed': 0,
            'locations_created': 0,
            'accommodations_created': 0,
            'price_forecasts_created': 0,
            'images_created': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            logger.info("="*60)
            logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
            logger.info("="*60)

            # Load hotel data for this city
            data_file_path = HotelLoadingConfig.get_data_file_path(city_key, 'core')
            hotels_data = FileManager.load_json_data(data_file_path)

            if not hotels_data:
                city_stats['errors'].append(f"No hotel data found for {city_config['city_name']}")
                city_stats['status'] = 'failed'
                return city_stats

            city_stats['hotels_found'] = len(hotels_data)
            logger.info(f"Found {city_stats['hotels_found']} hotels in {city_config['city_name']}")

            # Get accommodation type ID
            accommodation_type_id = self.db.get_accommodation_type_id('Hotel')

            # Initialize progress tracker
            progress = ProgressTracker(city_stats['hotels_found'])

            # Process each hotel
            for hotel_index, (hotel_id, hotel_data) in enumerate(hotels_data.items()):
                try:
                    logger.info(f"Processing hotel {hotel_index + 1}/{city_stats['hotels_found']}: {hotel_id}")

                    # Process this hotel
                    result = self.process_single_hotel(
                        hotel_id,
                        hotel_data,
                        city_config['city_id'],
                        accommodation_type_id
                    )

                    if result['success']:
                        # Commit this individual hotel
                        self.db.commit()
                        city_stats['hotels_processed'] += 1
                        city_stats['locations_created'] += result['location_created']
                        city_stats['accommodations_created'] += result['accommodation_created']
                        city_stats['price_forecasts_created'] += result['price_forecasts_created']
                        city_stats['images_created'] += result['images_created']
                        logger.info(f"[OK] Hotel processed and committed successfully")
                    else:
                        # Rollback this individual hotel but continue with others
                        self.db.rollback()
                        city_stats['errors'].append(f"Hotel {hotel_index + 1}: {result['error']}")
                        logger.error(f"[ERROR] Hotel processing failed: {result['error']}")

                    progress.update()

                except Exception as e:
                    # Rollback this individual hotel but continue with others
                    self.db.rollback()
                    error_msg = f"Hotel {hotel_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    city_stats['errors'].append(error_msg)

            city_stats['status'] = 'completed'
            logger.info(f"[OK] {city_config['city_name']} processing completed")
            logger.info(f"  Hotels processed: {city_stats['hotels_processed']}/{city_stats['hotels_found']}")
            logger.info(f"  Locations created: {city_stats['locations_created']}")
            logger.info(f"  Accommodations created: {city_stats['accommodations_created']}")
            logger.info(f"  Price forecasts created: {city_stats['price_forecasts_created']}")
            logger.info(f"  Images created: {city_stats['images_created']}")
            logger.info(f"  Errors: {len(city_stats['errors'])}")

        except Exception as e:
            # City-level error
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def load_core_data(self, target_cities: Dict) -> Dict:
        """Load core data for all target cities"""
        logger.info("[START] STARTING CORE DATA LOADING")
        logger.info("="*80)

        start_time = datetime.now()
        results = {'cities': {}, 'overall': self.stats.copy()}

        try:
            # Process each target city
            for city_key, city_config in target_cities.items():
                city_result = self.process_city(city_key, city_config)
                results['cities'][city_key] = city_result

                # Update overall statistics
                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['hotels_processed'] += city_result['hotels_processed']
                    self.stats['locations_created'] += city_result['locations_created']
                    self.stats['accommodations_created'] += city_result['accommodations_created']
                    self.stats['price_forecasts_created'] += city_result['price_forecasts_created']
                    self.stats['images_created'] += city_result['images_created']

                self.stats['errors'].extend(city_result['errors'])

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("\n" + "="*80)
            logger.info("[SUCCESS] CORE DATA LOADING COMPLETED")
            logger.info("="*80)
            logger.info(f"[TIME] Duration: {duration}")
            logger.info(f"[CITIES] Cities: {self.stats['cities_processed']}/{len(target_cities)}")
            logger.info(f"[HOTELS] Hotels: {self.stats['hotels_processed']}")
            logger.info(f"[LOCATIONS] Locations: {self.stats['locations_created']}")
            logger.info(f"[ACCOMMODATIONS] Accommodations: {self.stats['accommodations_created']}")
            logger.info(f"[FORECASTS] Price Forecasts: {self.stats['price_forecasts_created']}")
            logger.info(f"[IMAGES] Images: {self.stats['images_created']}")
            logger.info(f"[ERRORS] Errors: {len(self.stats['errors'])}")

            results['overall'] = self.stats
            return results

        except Exception as e:
            logger.error(f"[ERROR] Core data loading failed: {e}")
            logger.error(traceback.format_exc())
            results['overall']['errors'].append(str(e))
            return results
