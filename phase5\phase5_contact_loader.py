#!/usr/bin/env python3
"""
Phase 5: Contact Data Loader
Loads contact information (phone, website, address) for accommodations.

This script follows the same patterns as Phase 3 and Phase 4:
- City-by-city processing approach
- Individual transaction handling per hotel
- External ID system for data matching
- Comprehensive error handling and logging
- Easy scalability for new cities

Contact data mapping:
- contact.phone → accommodation.phone
- contact.website → accommodation.website
- contact.street_address + contact.postal_code → location.address
"""

import os
import sys
import json
import logging
import traceback
import re
from pathlib import Path
from typing import Dict, Optional, Tuple
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase5_contact_loader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnection:
    """Database connection manager - same as previous phases"""

    def __init__(self, db_config: Dict):
        self.config = db_config
        self.connection = None

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.config)
            self.connection.autocommit = False
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")

    def get_cursor(self):
        """Get database cursor with RealDictCursor for named access"""
        if not self.connection:
            self.connect()
        return self.connection.cursor(cursor_factory=RealDictCursor)

class ContactDataProcessor:
    """Contact data processor - follows same patterns as Phase 3/4"""

    def __init__(self, db_connection: DatabaseConnection, data_directory: str):
        self.db = db_connection
        self.data_dir = Path(data_directory)

        # Data paths - adjust for running from phase5 directory
        self.processed_data_path = "../processed_data"
        self.contacts_data_path = "../processed_data/contacts"

        # Target cities configuration - same pattern as Phase 3/4
        self.target_cities = {
            'casablanca': {
                'city_name': 'Casablanca',
                'expected_city_id': 4,
                'processed_file': 'casablanca.json'
            },
            'marrakech': {
                'city_name': 'Marrakech',
                'expected_city_id': 7,
                'processed_file': 'marrakech.json'
            }
        }

        # Processing statistics
        self.stats = {
            'cities_processed': 0,
            'contacts_processed': 0,
            'accommodations_updated': 0,
            'locations_updated': 0,
            'phones_added': 0,
            'websites_added': 0,
            'addresses_added': 0,
            'errors': [],
            'city_results': {}
        }

    def setup_logging(self):
        """Configure logging - same as Phase 4"""
        return logging.getLogger(__name__)

    def load_contact_data(self, city_key: str) -> Dict:
        """Load contact data for a city"""
        try:
            file_path = os.path.join(self.contacts_data_path, self.target_cities[city_key]['processed_file'])

            if not os.path.exists(file_path):
                logger.error(f"Contact data file not found: {file_path}")
                return {}

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"✓ Loaded {len(data)} contact records from {file_path}")
                return data

        except Exception as e:
            logger.error(f"Error loading contact data for {city_key}: {e}")
            return {}

    def get_accommodation_and_location_by_external_id(self, external_id: str, city_id: int) -> Optional[Tuple[int, int]]:
        """Get accommodation and location IDs by external ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT a.id as accommodation_id, a.location_id
                    FROM accommodation a
                    JOIN location l ON a.location_id = l.id
                    WHERE a.external_id = %s AND l.city_id = %s
                """, (external_id, city_id))

                result = cursor.fetchone()
                return (result['accommodation_id'], result['location_id']) if result else None

        except Exception as e:
            logger.error(f"Error getting accommodation by external ID {external_id}: {e}")
            return None

    def validate_phone_number(self, phone: str) -> Optional[str]:
        """Validate and clean phone number"""
        if not phone or not isinstance(phone, str):
            return None

        # Remove extra whitespace
        phone = phone.strip()

        # Basic validation - should contain digits and common phone characters
        if not re.search(r'\d', phone):
            return None

        # Check length constraints (accommodation.phone is varchar(50))
        if len(phone) > 50:
            logger.warning(f"Phone number too long, truncating: {phone[:50]}...")
            phone = phone[:50]

        return phone

    def validate_website_url(self, website: str) -> Optional[str]:
        """Validate and clean website URL"""
        if not website or not isinstance(website, str):
            return None

        # Remove extra whitespace
        website = website.strip()

        # Basic URL validation
        if not (website.startswith('http://') or website.startswith('https://')):
            return None

        # Check if it's a reasonable URL (contains domain-like structure)
        if not re.search(r'https?://[^\s/$.?#].[^\s]*', website):
            return None

        return website

    def combine_address_parts(self, street_address: str, postal_code: str) -> Optional[str]:
        """Combine street address and postal code into full address"""
        parts = []

        if street_address and isinstance(street_address, str):
            parts.append(street_address.strip())

        if postal_code and isinstance(postal_code, str):
            parts.append(postal_code.strip())

        if not parts:
            return None

        return ', '.join(parts)

    def update_accommodation_contact(self, accommodation_id: int, location_id: int, contact_data: Dict) -> Dict:
        """Update accommodation and location with contact information"""
        result = {
            'success': False,
            'accommodation_updated': False,
            'location_updated': False,
            'phone_added': False,
            'website_added': False,
            'address_added': False,
            'error': None
        }

        try:
            # Extract and validate contact data
            phone = self.validate_phone_number(contact_data.get('phone'))
            website = self.validate_website_url(contact_data.get('website'))
            address = self.combine_address_parts(
                contact_data.get('street_address'),
                contact_data.get('postal_code')
            )

            with self.db.get_cursor() as cursor:
                # Update accommodation with phone and website
                if phone or website:
                    update_fields = []
                    update_values = []

                    if phone:
                        update_fields.append("phone = %s")
                        update_values.append(phone)
                        result['phone_added'] = True

                    if website:
                        update_fields.append("website = %s")
                        update_values.append(website)
                        result['website_added'] = True

                    if update_fields:
                        update_fields.append("updated_at = now()")
                        update_values.append(accommodation_id)

                        update_query = f"""
                            UPDATE accommodation
                            SET {', '.join(update_fields)}
                            WHERE id = %s
                        """

                        cursor.execute(update_query, update_values)

                        if cursor.rowcount > 0:
                            result['accommodation_updated'] = True
                            logger.debug(f"    ✓ Updated accommodation contact info")

                # Update location with address
                if address:
                    cursor.execute("""
                        UPDATE location
                        SET address = %s, updated_at = now()
                        WHERE id = %s
                    """, (address, location_id))

                    if cursor.rowcount > 0:
                        result['location_updated'] = True
                        result['address_added'] = True
                        logger.debug(f"    ✓ Updated location address: {address}")

                result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"    ✗ Contact update error: {e}")

        return result

    def process_single_contact(self, external_id: str, contact_data: Dict, city_id: int) -> Dict:
        """Process contact information for a single accommodation"""
        result = {
            'success': False,
            'accommodation_updated': 0,
            'location_updated': 0,
            'phone_added': 0,
            'website_added': 0,
            'address_added': 0,
            'error': None
        }

        try:
            # Get accommodation and location IDs
            ids = self.get_accommodation_and_location_by_external_id(external_id, city_id)
            if not ids:
                result['error'] = f"No accommodation found for external ID {external_id}"
                return result

            accommodation_id, location_id = ids

            # Update contact information
            update_result = self.update_accommodation_contact(accommodation_id, location_id, contact_data)

            if update_result['success']:
                result['success'] = True
                result['accommodation_updated'] = 1 if update_result['accommodation_updated'] else 0
                result['location_updated'] = 1 if update_result['location_updated'] else 0
                result['phone_added'] = 1 if update_result['phone_added'] else 0
                result['website_added'] = 1 if update_result['website_added'] else 0
                result['address_added'] = 1 if update_result['address_added'] else 0
            else:
                result['error'] = update_result['error']

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"  ✗ Contact processing error: {e}")
            logger.error(traceback.format_exc())

        return result

    def process_city(self, city_key: str) -> Dict:
        """Process all contact records for a city - same pattern as Phase 3/4"""
        city_config = self.target_cities[city_key]
        city_stats = {
            'city_name': city_config['city_name'],
            'contacts_found': 0,
            'contacts_processed': 0,
            'accommodations_updated': 0,
            'locations_updated': 0,
            'phones_added': 0,
            'websites_added': 0,
            'addresses_added': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            logger.info("="*60)
            logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
            logger.info("="*60)

            # Load contact data for this city
            contacts_data = self.load_contact_data(city_key)
            if not contacts_data:
                city_stats['errors'].append(f"No contact data found for {city_config['city_name']}")
                city_stats['status'] = 'failed'
                return city_stats

            city_stats['contacts_found'] = len(contacts_data)
            logger.info(f"Found {city_stats['contacts_found']} contact records in {city_config['city_name']}")

            # Process each contact record
            for contact_index, (external_id, contact_data) in enumerate(contacts_data.items()):
                try:
                    logger.info(f"Processing contact {contact_index + 1}/{city_stats['contacts_found']}: External ID {external_id}")

                    # Process this contact record
                    result = self.process_single_contact(
                        external_id,
                        contact_data,
                        city_config['expected_city_id']
                    )

                    if result['success']:
                        # Commit this individual contact update
                        self.db.connection.commit()
                        city_stats['contacts_processed'] += 1
                        city_stats['accommodations_updated'] += result['accommodation_updated']
                        city_stats['locations_updated'] += result['location_updated']
                        city_stats['phones_added'] += result['phone_added']
                        city_stats['websites_added'] += result['website_added']
                        city_stats['addresses_added'] += result['address_added']
                        logger.info(f"✓ Contact processed and committed successfully")
                    else:
                        # Rollback this individual contact but continue with others
                        self.db.connection.rollback()
                        city_stats['errors'].append(f"Contact {contact_index + 1}: {result['error']}")
                        logger.error(f"✗ Contact processing failed: {result['error']}")

                except Exception as e:
                    # Rollback this individual contact but continue with others
                    self.db.connection.rollback()
                    error_msg = f"Contact {contact_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    city_stats['errors'].append(error_msg)

                # Progress update every 100 contacts
                if (contact_index + 1) % 100 == 0:
                    logger.info(f"  Progress: {contact_index + 1}/{city_stats['contacts_found']} contacts processed")

            city_stats['status'] = 'completed'
            logger.info(f"✓ {city_config['city_name']} processing completed")
            logger.info(f"  Contacts processed: {city_stats['contacts_processed']}/{city_stats['contacts_found']}")
            logger.info(f"  Accommodations updated: {city_stats['accommodations_updated']}")
            logger.info(f"  Locations updated: {city_stats['locations_updated']}")
            logger.info(f"  Phones added: {city_stats['phones_added']}")
            logger.info(f"  Websites added: {city_stats['websites_added']}")
            logger.info(f"  Addresses added: {city_stats['addresses_added']}")
            logger.info(f"  Errors: {len(city_stats['errors'])}")

        except Exception as e:
            # City-level error
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def run_contact_loading(self):
        """Main execution method - same pattern as Phase 3/4"""
        logger.info("🚀 PHASE 5: CONTACT DATA LOADING STARTED")
        logger.info("="*80)

        start_time = datetime.now()

        try:
            # Process each target city
            for city_key in self.target_cities.keys():
                city_result = self.process_city(city_key)
                self.stats['city_results'][city_key] = city_result

                # Update overall statistics
                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['contacts_processed'] += city_result['contacts_processed']
                    self.stats['accommodations_updated'] += city_result['accommodations_updated']
                    self.stats['locations_updated'] += city_result['locations_updated']
                    self.stats['phones_added'] += city_result['phones_added']
                    self.stats['websites_added'] += city_result['websites_added']
                    self.stats['addresses_added'] += city_result['addresses_added']

                self.stats['errors'].extend(city_result['errors'])

            # Final summary
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info("\n" + "="*80)
            logger.info("🎉 PHASE 5: CONTACT DATA LOADING COMPLETED")
            logger.info("="*80)
            logger.info(f"⏱️  Total Duration: {duration}")
            logger.info(f"🏙️  Cities Processed: {self.stats['cities_processed']}/{len(self.target_cities)}")
            logger.info(f"📞 Contacts Processed: {self.stats['contacts_processed']}")
            logger.info(f"🏨 Accommodations Updated: {self.stats['accommodations_updated']}")
            logger.info(f"📍 Locations Updated: {self.stats['locations_updated']}")
            logger.info(f"📞 Phones Added: {self.stats['phones_added']}")
            logger.info(f"🌐 Websites Added: {self.stats['websites_added']}")
            logger.info(f"📮 Addresses Added: {self.stats['addresses_added']}")
            logger.info(f"❌ Total Errors: {len(self.stats['errors'])}")

            # City-by-city breakdown
            logger.info("\n📊 CITY-BY-CITY RESULTS:")
            for city_key, city_result in self.stats['city_results'].items():
                status_emoji = "✅" if city_result['status'] == 'completed' else "❌"
                logger.info(f"  {status_emoji} {city_result['city_name']}:")
                logger.info(f"    Contacts: {city_result['contacts_processed']}/{city_result['contacts_found']}")
                logger.info(f"    Accommodations: {city_result['accommodations_updated']}")
                logger.info(f"    Phones: {city_result['phones_added']}")
                logger.info(f"    Websites: {city_result['websites_added']}")
                logger.info(f"    Addresses: {city_result['addresses_added']}")
                logger.info(f"    Errors: {len(city_result['errors'])}")

            # Success rate calculation
            if self.stats['contacts_processed'] > 0:
                success_rate = (self.stats['accommodations_updated'] / self.stats['contacts_processed']) * 100
                logger.info(f"\n📈 Overall Success Rate: {success_rate:.1f}%")

            logger.info("\n🎯 Next Steps:")
            logger.info("  1. Run validation queries: python run_contact_validation.py")
            logger.info("  2. Check contact coverage and data quality")
            logger.info("  3. Review any errors in the log file")
            logger.info("="*80)

            return True

        except Exception as e:
            logger.error(f"❌ Contact loading failed: {e}")
            logger.error(traceback.format_exc())
            return False

def main():
    """Main function - same pattern as Phase 3/4"""
    try:
        # Database configuration - matches previous phases
        db_config = {
            'host': 'localhost',
            'database': 'testing_full_database',
            'user': 'postgres',
            'password': '1234',
            'port': 5432
        }

        # Initialize database connection
        db_connection = DatabaseConnection(db_config)

        # Initialize contact processor
        processor = ContactDataProcessor(db_connection, "../processed_data")

        # Run contact loading
        success = processor.run_contact_loading()

        # Close database connection
        db_connection.close()

        # Exit with appropriate code
        sys.exit(0 if success else 1)

    except Exception as e:
        logger.error(f"❌ Main execution failed: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()