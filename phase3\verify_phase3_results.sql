-- =====================================================
-- PHASE 3 RESULTS VERIFICATION QUERIES
-- Execute these queries AFTER running Phase 3 to verify success
-- =====================================================

-- QUERY 1: Check total accommodations created
SELECT 'Total Accommodations Check' as test_name;
SELECT 
    COUNT(*) as total_accommodations,
    '✓ ACCOMMODATIONS LOADED' as status
FROM accommodation;

-- QUERY 2: Check accommodations by city
SELECT 'Accommodations by City' as test_name;
SELECT 
    c.name as city_name,
    COUNT(a.id) as hotel_count,
    '✓ CITY DATA LOADED' as status
FROM city c
LEFT JOIN location l ON c.id = l.city_id
LEFT JOIN accommodation a ON l.id = a.location_id
WHERE c.country_id = 1
GROUP BY c.id, c.name
ORDER BY hotel_count DESC;

-- QUERY 3: Check locations created
SELECT 'Locations Created Check' as test_name;
SELECT 
    COUNT(*) as total_locations,
    COUNT(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN 1 END) as locations_with_coordinates,
    '✓ LOCATIONS CREATED' as status
FROM location 
WHERE country_id = 1;

-- QUERY 4: Check accommodation details
SELECT 'Accommodation Details Check' as test_name;
SELECT 
    COUNT(*) as total_hotels,
    COUNT(CASE WHEN stars IS NOT NULL THEN 1 END) as hotels_with_stars,
    COUNT(CASE WHEN average_score IS NOT NULL THEN 1 END) as hotels_with_scores,
    COUNT(CASE WHEN website IS NOT NULL THEN 1 END) as hotels_with_websites,
    COUNT(CASE WHEN phone IS NOT NULL THEN 1 END) as hotels_with_phones,
    '✓ HOTEL DETAILS LOADED' as status
FROM accommodation;

-- QUERY 5: Check star ratings distribution
SELECT 'Star Ratings Distribution' as test_name;
SELECT 
    COALESCE(stars, 0) as star_rating,
    COUNT(*) as hotel_count,
    '✓ RATINGS LOADED' as status
FROM accommodation
GROUP BY stars
ORDER BY stars;

-- QUERY 6: Check accommodation types
SELECT 'Accommodation Types Check' as test_name;
SELECT 
    at.name as accommodation_type,
    COUNT(a.id) as hotel_count,
    '✓ TYPES ASSIGNED' as status
FROM accommodation_type at
LEFT JOIN accommodation a ON at.id = a.type_id
GROUP BY at.id, at.name
ORDER BY hotel_count DESC;

-- QUERY 7: Sample hotels from each target city
SELECT 'Sample Hotels by City' as test_name;
WITH city_hotels AS (
    SELECT 
        c.name as city_name,
        a.name as hotel_name,
        a.stars,
        a.average_score,
        ROW_NUMBER() OVER (PARTITION BY c.name ORDER BY a.name) as rn
    FROM city c
    JOIN location l ON c.id = l.city_id
    JOIN accommodation a ON l.id = a.location_id
    WHERE c.name IN ('Casablanca', 'Marrakech')
)
SELECT 
    city_name,
    hotel_name,
    stars,
    average_score,
    '✓ SAMPLE DATA' as status
FROM city_hotels 
WHERE rn <= 3
ORDER BY city_name, hotel_name;

-- QUERY 8: Check for data quality issues
SELECT 'Data Quality Check' as test_name;
SELECT 
    'Hotels without names' as issue_type,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✓ NO ISSUES' ELSE '⚠️ NEEDS ATTENTION' END as status
FROM accommodation 
WHERE name IS NULL OR TRIM(name) = ''
UNION ALL
SELECT 
    'Locations without cities' as issue_type,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✓ NO ISSUES' ELSE '⚠️ NEEDS ATTENTION' END as status
FROM location 
WHERE city_id IS NULL
UNION ALL
SELECT 
    'Accommodations without locations' as issue_type,
    COUNT(*) as count,
    CASE WHEN COUNT(*) = 0 THEN '✓ NO ISSUES' ELSE '⚠️ NEEDS ATTENTION' END as status
FROM accommodation 
WHERE location_id IS NULL;

-- QUERY 9: Check coordinate data
SELECT 'Coordinate Data Check' as test_name;
SELECT 
    c.name as city_name,
    COUNT(*) as total_hotels,
    COUNT(CASE WHEN l.latitude IS NOT NULL AND l.longitude IS NOT NULL THEN 1 END) as hotels_with_coordinates,
    ROUND(
        (COUNT(CASE WHEN l.latitude IS NOT NULL AND l.longitude IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)), 
        1
    ) as coordinate_percentage,
    '✓ COORDINATES CHECKED' as status
FROM city c
JOIN location l ON c.id = l.city_id
JOIN accommodation a ON l.id = a.location_id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.id, c.name
ORDER BY c.name;

-- QUERY 10: Check recent data (created today)
SELECT 'Recent Data Check' as test_name;
SELECT 
    'Accommodations created today' as data_type,
    COUNT(*) as count,
    '✓ NEW DATA' as status
FROM accommodation 
WHERE DATE(created_at) = CURRENT_DATE
UNION ALL
SELECT 
    'Locations created today' as data_type,
    COUNT(*) as count,
    '✓ NEW DATA' as status
FROM location 
WHERE DATE(created_at) = CURRENT_DATE;

-- SUMMARY QUERY: Overall Phase 3 Status
SELECT 'PHASE 3 OVERALL STATUS' as test_name;
SELECT 
    'Target cities data' as component,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM city c
            JOIN location l ON c.id = l.city_id
            JOIN accommodation a ON l.id = a.location_id
            WHERE c.name IN ('Casablanca', 'Marrakech')
        )
        THEN '✓ SUCCESS - Hotels loaded for target cities'
        ELSE '✗ FAILED - No hotels found for target cities'
    END as status
UNION ALL
SELECT 
    'Data integrity' as component,
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM accommodation WHERE name IS NULL OR TRIM(name) = '')
        AND NOT EXISTS (SELECT 1 FROM location WHERE city_id IS NULL)
        AND NOT EXISTS (SELECT 1 FROM accommodation WHERE location_id IS NULL)
        THEN '✓ SUCCESS - Data integrity maintained'
        ELSE '⚠️ WARNING - Data quality issues found'
    END as status
UNION ALL
SELECT 
    'Hotel accommodation type' as component,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM accommodation a
            JOIN accommodation_type at ON a.type_id = at.id
            WHERE at.name = 'Hotel'
        )
        THEN '✓ SUCCESS - Hotels properly typed'
        ELSE '✗ FAILED - Hotel type not assigned'
    END as status;

-- FINAL STATISTICS
SELECT 'PHASE 3 FINAL STATISTICS' as summary;
SELECT 
    (SELECT COUNT(*) FROM accommodation) as total_accommodations,
    (SELECT COUNT(*) FROM location WHERE country_id = 1) as total_locations,
    (SELECT COUNT(*) FROM accommodation a 
     JOIN location l ON a.location_id = l.id 
     JOIN city c ON l.city_id = c.id 
     WHERE c.name IN ('Casablanca', 'Marrakech')) as target_city_hotels,
    '🎉 PHASE 3 VERIFICATION COMPLETE' as message;
