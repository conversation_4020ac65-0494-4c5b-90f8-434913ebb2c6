-- ============================================================================
-- BASIC COUNTS AND STATISTICS
-- Comprehensive count queries for all tables in the hotel database
-- ============================================================================

\echo '============================================================================'
\echo 'HOTEL DATABASE VERIFICATION - BASIC COUNTS'
\echo '============================================================================'
\echo ''

-- Core Tables Count Summary
\echo '--- CORE TABLES SUMMARY ---'
SELECT 
    'accommodations' as table_name,
    COUNT(*) as total_records
FROM accommodation
UNION ALL
SELECT 
    'locations' as table_name,
    COUNT(*) as total_records
FROM location
UNION ALL
SELECT 
    'cities' as table_name,
    COUNT(*) as total_records
FROM city
UNION ALL
SELECT 
    'accommodation_types' as table_name,
    COUNT(*) as total_records
FROM accommodation_type
ORDER BY table_name;

\echo ''

-- Accommodation Details
\echo '--- ACCOMMODATION STATISTICS ---'
SELECT 
    COUNT(*) as total_accommodations,
    COUNT(CASE WHEN external_id IS NOT NULL THEN 1 END) as with_external_id,
    COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as with_phone,
    COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) as with_website,
    COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as with_email,
    COUNT(CASE WHEN stars IS NOT NULL THEN 1 END) as with_star_rating,
    COUNT(CASE WHEN average_score IS NOT NULL THEN 1 END) as with_average_score,
    COUNT(CASE WHEN description IS NOT NULL AND description != '' THEN 1 END) as with_description
FROM accommodation;

\echo ''

-- Location Statistics
\echo '--- LOCATION STATISTICS ---'
SELECT 
    COUNT(*) as total_locations,
    COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END) as with_address,
    COUNT(CASE WHEN lat IS NOT NULL AND lng IS NOT NULL THEN 1 END) as with_coordinates,
    COUNT(CASE WHEN type IS NOT NULL THEN 1 END) as with_location_type
FROM location;

\echo ''

-- City Distribution
\echo '--- ACCOMMODATIONS BY CITY ---'
SELECT 
    c.name as city_name,
    COUNT(a.id) as accommodation_count,
    COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) as with_external_id
FROM city c
LEFT JOIN location l ON c.id = l.city_id
LEFT JOIN accommodation a ON l.id = a.location_id
GROUP BY c.id, c.name
ORDER BY accommodation_count DESC;

\echo ''

-- Amenity and Review Statistics
\echo '--- AMENITY AND REVIEW STATISTICS ---'
SELECT 
    'amenities' as table_name,
    COUNT(*) as total_records
FROM amenity
UNION ALL
SELECT 
    'accommodation_amenity_mappings' as table_name,
    COUNT(*) as total_records
FROM accommodation_amenity_junction
UNION ALL
SELECT 
    'reviews' as table_name,
    COUNT(*) as total_records
FROM review
UNION ALL
SELECT 
    'entity_images' as table_name,
    COUNT(*) as total_records
FROM entity_image
ORDER BY table_name;

\echo ''

-- Star Rating Distribution
\echo '--- STAR RATING DISTRIBUTION ---'
SELECT 
    stars,
    COUNT(*) as accommodation_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM accommodation), 2) as percentage
FROM accommodation
WHERE stars IS NOT NULL
GROUP BY stars
ORDER BY stars;

\echo ''

-- External ID Coverage by City
\echo '--- EXTERNAL ID COVERAGE BY CITY ---'
SELECT 
    c.name as city_name,
    COUNT(a.id) as total_accommodations,
    COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) as with_external_id,
    ROUND(
        COUNT(CASE WHEN a.external_id IS NOT NULL THEN 1 END) * 100.0 / 
        NULLIF(COUNT(a.id), 0), 2
    ) as external_id_coverage_percent
FROM city c
LEFT JOIN location l ON c.id = l.city_id
LEFT JOIN accommodation a ON l.id = a.location_id
GROUP BY c.id, c.name
HAVING COUNT(a.id) > 0
ORDER BY external_id_coverage_percent DESC;

\echo ''
\echo '============================================================================'
\echo 'BASIC COUNTS VERIFICATION COMPLETED'
\echo '============================================================================'
