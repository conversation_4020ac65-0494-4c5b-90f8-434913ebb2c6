2025-05-25 20:14:01,039 - INFO - Database connection established successfully
2025-05-25 20:14:01,039 - INFO - === STARTING PHASE 2: REFERENCE DATA LOADING ===
2025-05-25 20:14:01,040 - INFO - Starting JSON file validation...
2025-05-25 20:14:01,946 - INFO - JSON validation complete: 2149 valid, 0 invalid
2025-05-25 20:14:01,947 - INFO - Creating city-filename mapping...
2025-05-25 20:14:01,951 - INFO - Mapped agadir.json to city ID 3
2025-05-25 20:14:01,951 - INFO - Mapped casablanca.json to city ID 4
2025-05-25 20:14:01,951 - INFO - Mapped chefchaouen.json to city ID 2
2025-05-25 20:14:01,951 - INFO - Mapped essaouira.json to city ID 5
2025-05-25 20:14:01,952 - WARNING - No city mapping found for fes.json
2025-05-25 20:14:01,952 - INFO - Mapped marrakech.json to city ID 7
2025-05-25 20:14:01,952 - INFO - Mapped merzouga.json to city ID 6
2025-05-25 20:14:01,952 - WARNING - No city mapping found for nador.json
2025-05-25 20:14:01,953 - INFO - Mapped rabat.json to city ID 9
2025-05-25 20:14:01,953 - INFO - Mapped tangier.json to city ID 10
2025-05-25 20:14:01,953 - INFO - Mapped tetouan.json to city ID 12
2025-05-25 20:14:01,953 - INFO - City mapping complete: 9 files mapped
2025-05-25 20:14:01,953 - INFO - Extracting amenities from JSON files...
2025-05-25 20:14:02,083 - INFO - Extracted 0 unique amenities
2025-05-25 20:14:02,083 - INFO - Loading amenities to database...
2025-05-25 20:14:02,084 - INFO - Loaded 0 new amenities, 0 already existed
2025-05-25 20:14:02,085 - INFO - === PHASE 2 COMPLETED SUCCESSFULLY ===
2025-05-25 20:14:02,085 - INFO - Statistics: {'files_validated': 2149, 'files_invalid': 0, 'amenities_processed': 0, 'cities_mapped': 9, 'errors': ['No city mapping found for fes.json', 'No city mapping found for nador.json']}
2025-05-25 20:14:02,086 - INFO - Database connection closed
