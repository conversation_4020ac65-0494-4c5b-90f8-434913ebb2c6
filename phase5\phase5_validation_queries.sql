-- =====================================================================================
-- PHASE 5: CONTACT DATA VALIDATION QUERIES
-- =====================================================================================
-- Comprehensive validation queries for contact data loading
-- Validates phone numbers, websites, addresses, and data coverage
-- =====================================================================================

-- =============================================================================
-- BASIC CONTACT DATA STATISTICS
-- =============================================================================

-- 1. Overall Contact Coverage Summary
SELECT
    'Contact Coverage Summary' as metric,
    COUNT(*) as total_accommodations,
    COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as accommodations_with_phone,
    COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) as accommodations_with_website,
    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as accommodations_with_address,
    COUNT(CASE WHEN (phone IS NOT NULL AND phone != '')
               OR (website IS NOT NULL AND website != '')
               OR (l.address IS NOT NULL AND l.address != '') THEN 1 END) as accommodations_with_any_contact,
    ROUND(
        (COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as phone_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as website_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as address_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN (phone IS NOT NULL AND phone != '')
                     OR (website IS NOT NULL AND website != '')
                     OR (l.address IS NOT NULL AND l.address != '') THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as overall_contact_coverage_pct
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- 2. Contact Coverage by City
SELECT
    c.name as city_name,
    COUNT(*) as total_accommodations,
    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as with_phone,
    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as with_website,
    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address,
    ROUND(
        (COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as phone_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as website_coverage_pct,
    ROUND(
        (COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as address_coverage_pct
FROM city c
JOIN location l ON c.id = l.city_id
JOIN accommodation acc ON l.id = acc.location_id
WHERE c.name IN ('Casablanca', 'Marrakech')
GROUP BY c.id, c.name
ORDER BY c.name;

-- 3. Contact Data Quality Analysis
SELECT
    'Contact Data Quality' as analysis_type,
    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as total_phones,
    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' AND acc.phone ~ '^\+?[0-9\(\)\-\s]+$' THEN 1 END) as valid_phone_format,
    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as total_websites,
    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' AND acc.website ~ '^https?://' THEN 1 END) as valid_website_format,
    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as total_addresses,
    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' AND LENGTH(l.address) > 10 THEN 1 END) as substantial_addresses
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- =============================================================================
-- DETAILED CONTACT ANALYSIS
-- =============================================================================

-- 4. Phone Number Analysis
SELECT
    'Phone Number Analysis' as metric,
    COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as phones_total,
    COUNT(CASE WHEN phone LIKE '+212%' THEN 1 END) as morocco_country_code,
    COUNT(CASE WHEN phone ~ '^\+?[0-9\(\)\-\s]+$' THEN 1 END) as valid_format,
    COUNT(CASE WHEN LENGTH(phone) BETWEEN 10 AND 20 THEN 1 END) as reasonable_length,
    AVG(LENGTH(phone)) as avg_phone_length,
    MIN(LENGTH(phone)) as min_phone_length,
    MAX(LENGTH(phone)) as max_phone_length
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech') AND phone IS NOT NULL AND phone != '';

-- 5. Website Analysis
SELECT
    'Website Analysis' as metric,
    COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) as websites_total,
    COUNT(CASE WHEN website LIKE 'https://%' THEN 1 END) as https_websites,
    COUNT(CASE WHEN website LIKE 'http://%' THEN 1 END) as http_websites,
    COUNT(CASE WHEN website LIKE '%trivago%' THEN 1 END) as trivago_redirects,
    COUNT(CASE WHEN website ~ '^https?://[^\s/$.?#].[^\s]*$' THEN 1 END) as valid_url_format,
    AVG(LENGTH(website)) as avg_website_length
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech') AND website IS NOT NULL AND website != '';

-- 6. Address Analysis
SELECT
    'Address Analysis' as metric,
    COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END) as addresses_total,
    COUNT(CASE WHEN address LIKE '%,%' THEN 1 END) as addresses_with_comma,
    COUNT(CASE WHEN address ~ '[0-9]{5}' THEN 1 END) as addresses_with_postal_code,
    COUNT(CASE WHEN LENGTH(address) > 20 THEN 1 END) as detailed_addresses,
    AVG(LENGTH(address)) as avg_address_length,
    MIN(LENGTH(address)) as min_address_length,
    MAX(LENGTH(address)) as max_address_length
FROM location l
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech') AND address IS NOT NULL AND address != '';

-- =============================================================================
-- SAMPLE DATA INSPECTION
-- =============================================================================

-- 7. Sample Accommodations with Complete Contact Information
SELECT
    acc.name as accommodation_name,
    acc.external_id,
    c.name as city_name,
    acc.phone,
    CASE
        WHEN LENGTH(acc.website) > 50
        THEN LEFT(acc.website, 47) || '...'
        ELSE acc.website
    END as website_preview,
    l.address
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
    AND acc.phone IS NOT NULL AND acc.phone != ''
    AND acc.website IS NOT NULL AND acc.website != ''
    AND l.address IS NOT NULL AND l.address != ''
ORDER BY c.name, acc.name
LIMIT 10;

-- 8. Sample Accommodations with Missing Contact Information
SELECT
    acc.name as accommodation_name,
    acc.external_id,
    c.name as city_name,
    CASE WHEN acc.phone IS NULL OR acc.phone = '' THEN 'Missing' ELSE 'Present' END as phone_status,
    CASE WHEN acc.website IS NULL OR acc.website = '' THEN 'Missing' ELSE 'Present' END as website_status,
    CASE WHEN l.address IS NULL OR l.address = '' THEN 'Missing' ELSE 'Present' END as address_status
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech')
    AND (
        (acc.phone IS NULL OR acc.phone = '') OR
        (acc.website IS NULL OR acc.website = '') OR
        (l.address IS NULL OR l.address = '')
    )
ORDER BY c.name, acc.name
LIMIT 10;

-- =============================================================================
-- BEFORE/AFTER COMPARISON ANALYSIS
-- =============================================================================

-- 9. Contact Data Improvement Analysis
-- Note: This shows current state - for before/after comparison,
-- run this query before and after Phase 5 loading
WITH contact_stats AS (
    SELECT
        c.name as city_name,
        COUNT(*) as total_accommodations,
        COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as current_phones,
        COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as current_websites,
        COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as current_addresses
    FROM accommodation acc
    JOIN location l ON acc.location_id = l.id
    JOIN city c ON l.city_id = c.id
    WHERE c.name IN ('Casablanca', 'Marrakech')
    GROUP BY c.name
)
SELECT
    city_name,
    total_accommodations,
    current_phones,
    current_websites,
    current_addresses,
    ROUND((current_phones::decimal / total_accommodations) * 100, 1) as phone_coverage_pct,
    ROUND((current_websites::decimal / total_accommodations) * 100, 1) as website_coverage_pct,
    ROUND((current_addresses::decimal / total_accommodations) * 100, 1) as address_coverage_pct
FROM contact_stats
ORDER BY city_name;

-- =============================================================================
-- DATA INTEGRITY CHECKS
-- =============================================================================

-- 10. Contact Data Integrity Verification
SELECT
    'Contact Data Integrity' as check_name,
    COUNT(*) as total_accommodations,
    COUNT(CASE WHEN acc.id IS NULL THEN 1 END) as orphaned_locations,
    COUNT(CASE WHEN l.id IS NULL THEN 1 END) as orphaned_accommodations,
    COUNT(CASE WHEN c.id IS NULL THEN 1 END) as orphaned_locations_no_city,
    CASE
        WHEN COUNT(CASE WHEN acc.id IS NULL OR l.id IS NULL OR c.id IS NULL THEN 1 END) = 0
        THEN 'PASS: All contact data properly linked'
        ELSE 'FAIL: Orphaned records found'
    END as integrity_result
FROM accommodation acc
FULL OUTER JOIN location l ON acc.location_id = l.id
FULL OUTER JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech') OR c.name IS NULL;

-- 11. External ID Coverage for Contact Matching
SELECT
    'External ID Coverage' as metric,
    COUNT(*) as total_accommodations,
    COUNT(CASE WHEN external_id IS NOT NULL AND external_id != '' THEN 1 END) as with_external_id,
    COUNT(CASE WHEN external_id IS NULL OR external_id = '' THEN 1 END) as without_external_id,
    ROUND(
        (COUNT(CASE WHEN external_id IS NOT NULL AND external_id != '' THEN 1 END)::decimal / COUNT(*)) * 100,
        1
    ) as external_id_coverage_pct
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
WHERE c.name IN ('Casablanca', 'Marrakech');

-- =============================================================================
-- PHASE 5 SUMMARY STATISTICS
-- =============================================================================

-- 12. Phase 5 Loading Summary
SELECT
    'Phase 5 Summary' as summary,
    (SELECT COUNT(*) FROM accommodation acc
     JOIN location l ON acc.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')) as total_accommodations,
    (SELECT COUNT(*) FROM accommodation acc
     JOIN location l ON acc.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')
     AND acc.phone IS NOT NULL AND acc.phone != '') as accommodations_with_phone,
    (SELECT COUNT(*) FROM accommodation acc
     JOIN location l ON acc.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')
     AND acc.website IS NOT NULL AND acc.website != '') as accommodations_with_website,
    (SELECT COUNT(*) FROM accommodation acc
     JOIN location l ON acc.location_id = l.id
     JOIN city c ON l.city_id = c.id
     WHERE c.name IN ('Casablanca', 'Marrakech')
     AND l.address IS NOT NULL AND l.address != '') as accommodations_with_address;

-- 13. Final Data Quality Score
WITH quality_metrics AS (
    SELECT
        COUNT(*) as total_accommodations,
        COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as with_phone,
        COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as with_website,
        COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address,
        COUNT(CASE WHEN (acc.phone IS NOT NULL AND acc.phone != '')
                     OR (acc.website IS NOT NULL AND acc.website != '')
                     OR (l.address IS NOT NULL AND l.address != '') THEN 1 END) as with_any_contact
    FROM accommodation acc
    JOIN location l ON acc.location_id = l.id
    JOIN city c ON l.city_id = c.id
    WHERE c.name IN ('Casablanca', 'Marrakech')
)
SELECT
    'Final Data Quality Score' as metric,
    total_accommodations,
    with_phone,
    with_website,
    with_address,
    with_any_contact,
    ROUND((with_phone::decimal / total_accommodations) * 100, 1) as phone_coverage_pct,
    ROUND((with_website::decimal / total_accommodations) * 100, 1) as website_coverage_pct,
    ROUND((with_address::decimal / total_accommodations) * 100, 1) as address_coverage_pct,
    ROUND((with_any_contact::decimal / total_accommodations) * 100, 1) as overall_contact_coverage_pct,
    ROUND(
        ((with_phone + with_website + with_address)::decimal / (total_accommodations * 3)) * 100,
        1
    ) as comprehensive_contact_quality_score
FROM quality_metrics;