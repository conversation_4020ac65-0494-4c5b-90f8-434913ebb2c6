-- =====================================================
-- PHASE 2 VERIFICATION QUERIES
-- Execute these queries to verify Phase 2 completed successfully
-- =====================================================

-- QUERY 1: Check if new tables were created
SELECT 'Schema Tables Check' as test_name;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('review', 'review_entry', 'review_aspect_rating', 'accommodation_price_forecast') 
        THEN '✓ NEW TABLE CREATED'
        ELSE '✓ EXISTING TABLE'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'accommodation', 'amenity', 'accommodation_amenity_junction',
    'review', 'review_entry', 'review_aspect_rating', 
    'accommodation_price_forecast'
)
ORDER BY table_name;

-- QUERY 2: Check if accommodation table has new columns
SELECT 'Accommodation Table Extensions' as test_name;
SELECT 
    column_name,
    data_type,
    is_nullable,
    CASE 
        WHEN column_name IN ('construction_year', 'highlights') 
        THEN '✓ NEW COLUMN ADDED'
        ELSE '✓ EXISTING COLUMN'
    END as status
FROM information_schema.columns 
WHERE table_name = 'accommodation' 
AND column_name IN ('construction_year', 'highlights', 'phone', 'stars')
ORDER BY column_name;

-- QUERY 3: Check if amenity junction table has new columns
SELECT 'Amenity Junction Table Extensions' as test_name;
SELECT 
    column_name,
    data_type,
    is_nullable,
    CASE 
        WHEN column_name IN ('is_free', 'is_available', 'is_top_amenity') 
        THEN '✓ NEW COLUMN ADDED'
        ELSE '✓ EXISTING COLUMN'
    END as status
FROM information_schema.columns 
WHERE table_name = 'accommodation_amenity_junction' 
AND column_name IN ('is_free', 'is_available', 'is_top_amenity', 'accommodation_id', 'amenity_id')
ORDER BY column_name;

-- QUERY 4: Check amenities loaded from Phase 2
SELECT 'Amenities Loading Check' as test_name;
SELECT 
    category,
    COUNT(*) as amenity_count,
    '✓ AMENITIES LOADED' as status
FROM amenity 
WHERE amenity_type = 'accommodation'
GROUP BY category
ORDER BY amenity_count DESC;

-- QUERY 5: Show total amenities count
SELECT 'Total Amenities Summary' as test_name;
SELECT 
    amenity_type,
    COUNT(*) as total_count,
    '✓ PHASE 2 SUCCESS' as status
FROM amenity 
GROUP BY amenity_type
ORDER BY total_count DESC;

-- QUERY 6: Check specific amenity examples (should show normalized names)
SELECT 'Amenity Normalization Examples' as test_name;
SELECT 
    name,
    category,
    '✓ NORMALIZED' as status
FROM amenity 
WHERE name IN ('WiFi', 'Parking', 'Pool', 'Restaurant', 'Air Conditioning', 'Spa')
AND amenity_type = 'accommodation'
ORDER BY name;

-- QUERY 7: Check cities available for Phase 3
SELECT 'Cities Available for Phase 3' as test_name;
SELECT 
    id as city_id,
    name as city_name,
    CASE 
        WHEN LOWER(name) IN ('casablanca', 'marrakech') 
        THEN '✓ READY FOR PHASE 3'
        ELSE '- Other city'
    END as phase3_status
FROM city 
WHERE country_id = 1
ORDER BY 
    CASE WHEN LOWER(name) IN ('casablanca', 'marrakech') THEN 1 ELSE 2 END,
    name;

-- QUERY 8: Check accommodation type 'Hotel' exists
SELECT 'Hotel Accommodation Type Check' as test_name;
SELECT 
    id,
    name,
    description,
    '✓ HOTEL TYPE READY' as status
FROM accommodation_type 
WHERE name = 'Hotel';

-- QUERY 9: Check foreign key constraints are in place
SELECT 'Foreign Key Constraints Check' as test_name;
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    '✓ CONSTRAINT ACTIVE' as status
FROM information_schema.table_constraints tc
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_name IN ('review_entry', 'review_aspect_rating', 'accommodation_price_forecast')
ORDER BY tc.table_name;

-- QUERY 10: Check indexes created for performance
SELECT 'Performance Indexes Check' as test_name;
SELECT 
    schemaname,
    tablename,
    indexname,
    '✓ INDEX CREATED' as status
FROM pg_indexes 
WHERE tablename IN ('review', 'review_entry', 'review_aspect_rating', 'accommodation_price_forecast')
AND schemaname = 'public'
ORDER BY tablename, indexname;

-- SUMMARY QUERY: Overall Phase 2 Status
SELECT 'PHASE 2 OVERALL STATUS' as test_name;
SELECT 
    'Schema modifications' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'review')
        THEN '✓ COMPLETED'
        ELSE '✗ FAILED'
    END as status
UNION ALL
SELECT 
    'Amenities loaded' as component,
    CASE 
        WHEN (SELECT COUNT(*) FROM amenity WHERE amenity_type = 'accommodation') > 0
        THEN '✓ COMPLETED (' || (SELECT COUNT(*) FROM amenity WHERE amenity_type = 'accommodation') || ' amenities)'
        ELSE '✗ FAILED'
    END as status
UNION ALL
SELECT 
    'Cities ready' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM city WHERE LOWER(name) IN ('casablanca', 'marrakech'))
        THEN '✓ READY FOR PHASE 3'
        ELSE '✗ CITIES MISSING'
    END as status;

-- FINAL MESSAGE
SELECT 
    '🎉 PHASE 2 VERIFICATION COMPLETE' as message,
    'If all checks show ✓ status, you are ready for Phase 3!' as next_step;
