# Phase 3 Data Validation Guide

## Overview
This guide explains how to run the comprehensive Phase 3 data validation to ensure all hotel data has been properly loaded before proceeding to Phase 4.

## Files Included
- `validate_phase3_complete.sql` - Comprehensive validation queries
- `run_validation.md` - This guide

## How to Run the Validation

### Option 1: Using pgAdmin (Recommended)
1. Open pgAdmin and connect to your database
2. Open the SQL query tool
3. Load the `validate_phase3_complete.sql` file
4. Execute the entire script
5. Review the results section by section

### Option 2: Using psql Command Line
```bash
# Connect to your database and run the validation
psql -h localhost -d your_database_name -U your_username -f validate_phase3_complete.sql
```

### Option 3: Using Database Management Tool
1. Open your preferred database management tool (DBeaver, DataGrip, etc.)
2. Connect to your PostgreSQL database
3. Open and execute the `validate_phase3_complete.sql` file

## What the Validation Tests

### Section 1: Hotel Data Completeness
- ✅ Hotel counts by city (should be ~700 each for Casablanca and Marrakech)
- ✅ Data completeness percentages for external IDs, stars, ratings, etc.
- ✅ Sample complete hotel records

### Section 2: Location Data Integrity
- ✅ Location data by city with coordinate coverage
- ✅ Hotels without locations (should be 0)
- ✅ Unused hotel locations (should be minimal)

### Section 3: Image Loading Verification
- ✅ Image loading summary and coverage percentages
- ✅ Images by city breakdown
- ✅ Sample image data with URLs

### Section 4: Price Forecast Data
- ✅ Price forecast summary with min/max/average prices
- ✅ Price forecasts by city and month
- ✅ Hotels without price forecasts

### Section 5: External ID Tracking
- ✅ External ID completeness and coverage
- ✅ External ID format validation
- ✅ Sample external ID mappings
- ✅ Duplicate external ID check (should be 0)

### Section 6: Data Relationships
- ✅ Foreign key integrity checks
- ✅ Accommodation type verification

### Section 7: Data Quality Checks
- ✅ Missing critical data identification
- ✅ Data range validation (stars 1-5, scores 0-10, etc.)
- ✅ Coordinate validation for Morocco

### Section 8: Sample Data Queries
- ✅ Complete hotel records with all associated data
- ✅ Hotels with most price forecasts
- ✅ Sample image URLs

### Section 9: Final Summary and Recommendations
- ✅ Overall Phase 3 completion summary
- ✅ Data quality score with percentages
- ✅ Phase 4 readiness check

## Expected Results

### Successful Phase 3 Loading Should Show:
- **Total Hotels**: ~1400 (700 Casablanca + 700 Marrakech)
- **External ID Coverage**: 95%+ of hotels have external IDs
- **Price Forecast Coverage**: 70%+ of hotels have price forecasts
- **Image Coverage**: 90%+ of hotels have images
- **Data Quality Assessment**: "EXCELLENT - Ready for Phase 4"

### Key Metrics to Check:
```
Cities processed: 2
Hotels processed: 1400
Locations created: 1350+
Accommodations created: 1400
Price forecasts created: 2800+
Images created: 1400
```

## Interpreting Results

### ✅ EXCELLENT Results
- External ID completeness: 95%+
- Price forecast coverage: 70%+
- Image coverage: 90%+
- **Status**: Ready for Phase 4

### ⚠️ GOOD Results
- External ID completeness: 90%+
- Some missing price forecasts or images
- **Status**: Minor issues to review, mostly ready for Phase 4

### ❌ NEEDS ATTENTION
- External ID completeness: <90%
- Significant missing data
- **Status**: Re-run Phase 3 or investigate data loading issues

## Troubleshooting Common Issues

### Issue: Low Hotel Count
**Expected**: ~1400 hotels total
**If Lower**: Check if Phase 3 completed successfully for both cities

### Issue: Missing External IDs
**Expected**: 95%+ coverage
**If Lower**: Check if `external_id` field was added to accommodation table

### Issue: No Price Forecasts
**Expected**: 2800+ price forecasts
**If Zero**: Check if JSON files contain `forcasted_prices` data

### Issue: No Images
**Expected**: 1400+ images
**If Zero**: Check if JSON files contain `image_link` data

### Issue: Broken Relationships
**Expected**: 0 broken foreign key references
**If Higher**: Check database constraints and data integrity

## Next Steps

### If Validation Passes (EXCELLENT/GOOD):
1. ✅ Phase 3 is complete
2. ✅ All data properly loaded
3. ✅ Ready to proceed to Phase 4 (Amenity and Review Relationships)

### If Validation Fails (NEEDS ATTENTION):
1. ❌ Review specific error sections
2. ❌ Check Phase 3 logs for errors
3. ❌ Re-run Phase 3 if necessary
4. ❌ Fix database schema issues if identified

## Sample Expected Output

```
1. HOTEL DATA COMPLETENESS
Hotel Counts by City:
- Casablanca: 700 hotels, 700 with external IDs
- Marrakech: 700 hotels, 700 with external IDs

Data Completeness Summary:
- Total hotels: 1400
- External ID percentage: 100.00%
- Stars percentage: 95.50%
- Ratings percentage: 98.20%

9. FINAL SUMMARY
Phase 3 Completion Summary:
- Target cities: 2
- Cities with hotels: 2
- Total hotels loaded: 1400
- Hotels with external IDs: 1400
- Total price forecasts: 2856
- Total images: 1400

Data Quality Score:
- External ID completeness: 100.00%
- Price forecast coverage: 85.50%
- Image coverage: 100.00%
- Overall assessment: EXCELLENT - Ready for Phase 4

Phase 4 Readiness Check:
- External ID status: ✅ All hotels have external IDs
- Price forecast status: ✅ Price forecasts loaded successfully
- Image status: ✅ Images loaded successfully
```

## Contact
If you encounter issues with the validation or need clarification on results, review the Phase 3 implementation or check the database schema documentation.
