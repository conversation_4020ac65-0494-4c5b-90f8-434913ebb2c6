#!/usr/bin/env python3
"""
Test script to examine JSON structure for Casablanca and Marrakech
Helps understand the data format before running Phase 3
"""

import json
import sys
from pathlib import Path

def examine_city_json(data_directory: str, city_filename: str):
    """Examine the structure of a city JSON file"""

    data_dir = Path(data_directory)
    city_file = data_dir / city_filename

    print(f"\n{'='*60}")
    print(f"EXAMINING: {city_filename}")
    print(f"{'='*60}")

    if not city_file.exists():
        print(f"❌ File not found: {city_file}")
        return False

    try:
        with open(city_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"✅ File loaded successfully")
        print(f"📊 File size: {city_file.stat().st_size / 1024:.1f} KB")

        # Examine top-level structure
        print(f"\n📋 TOP-LEVEL KEYS:")
        if isinstance(data, dict):
            for key in data.keys():
                value = data[key]
                if isinstance(value, list):
                    print(f"  {key}: list with {len(value)} items")
                elif isinstance(value, dict):
                    print(f"  {key}: dict with {len(value)} keys")
                else:
                    print(f"  {key}: {type(value).__name__}")
        else:
            print(f"  Root is {type(data).__name__}, not dict")
            return False

        # Examine hotels data - actual structure has hotel IDs as root keys
        hotel_entries = []
        for key, value in data.items():
            if isinstance(value, dict) and 'hotel_details' in value:
                hotel_entries.append((key, value))

        print(f"\n🏨 HOTELS DATA:")
        print(f"  Total hotels: {len(hotel_entries)}")

        if len(hotel_entries) > 0:
            # Examine first hotel structure
            first_hotel_id, first_hotel_entry = hotel_entries[0]
            first_hotel_details = first_hotel_entry.get('hotel_details', {})

            print(f"\n🔍 FIRST HOTEL STRUCTURE:")
            print(f"  Hotel ID: {first_hotel_id}")
            print(f"  Hotel name: {first_hotel_details.get('name', 'N/A')}")
            print(f"  Distance: {first_hotel_entry.get('distance_label', 'N/A')}")

            print(f"\n  Hotel entry structure:")
            for key, value in first_hotel_entry.items():
                if isinstance(value, dict):
                    print(f"    {key}: dict with keys {list(value.keys())}")
                elif isinstance(value, list):
                    print(f"    {key}: list with {len(value)} items")
                else:
                    print(f"    {key}: {type(value).__name__} = {str(value)[:50]}")

            print(f"\n  Hotel details structure:")
            for key, value in first_hotel_details.items():
                if isinstance(value, dict):
                    print(f"    {key}: dict with keys {list(value.keys())}")
                elif isinstance(value, list):
                    print(f"    {key}: list with {len(value)} items")
                else:
                    print(f"    {key}: {type(value).__name__} = {str(value)[:50]}")

            # Check coordinates structure
            coordinates = first_hotel_details.get('coordinates', {})
            if coordinates:
                print(f"\n🗺️ COORDINATES STRUCTURE:")
                for key, value in coordinates.items():
                    print(f"    {key}: {value}")

            # Check rating structure
            rating = first_hotel_details.get('rating', {})
            if rating:
                print(f"\n⭐ RATING STRUCTURE:")
                for key, value in rating.items():
                    print(f"    {key}: {value}")

            # Show sample of more hotels
            if len(hotel_entries) > 1:
                print(f"\n📝 SAMPLE HOTEL NAMES (first 5):")
                for i, (hotel_id, hotel_entry) in enumerate(hotel_entries[:5]):
                    hotel_details = hotel_entry.get('hotel_details', {})
                    name = hotel_details.get('name', 'Unknown')
                    stars = hotel_details.get('hotel_stars', 'N/A')
                    rating = hotel_details.get('rating', {}).get('rating', 'N/A')
                    print(f"  {i+1}. {name} ({stars} stars, {rating} rating)")

        return True

    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def main():
    """Main test function"""

    # Data directory path - UPDATE THIS PATH
    DATA_DIRECTORY = "processed_data"

    if not Path(DATA_DIRECTORY).exists():
        print(f"❌ Data directory '{DATA_DIRECTORY}' does not exist!")
        print("Please update the DATA_DIRECTORY variable in this script.")
        sys.exit(1)

    print("🔍 JSON STRUCTURE EXAMINATION FOR PHASE 3")
    print("Target cities: Casablanca and Marrakech")

    # Test both target cities
    cities_to_test = [
        'casablanca.json',
        'marrakech.json'
    ]

    results = {}
    for city_file in cities_to_test:
        results[city_file] = examine_city_json(DATA_DIRECTORY, city_file)

    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")

    for city_file, success in results.items():
        status = "✅ Ready" if success else "❌ Issues"
        print(f"{city_file}: {status}")

    all_ready = all(results.values())
    if all_ready:
        print(f"\n🎉 All target cities are ready for Phase 3!")
        print(f"You can now run: python phase3_core_data_loader.py")
    else:
        print(f"\n⚠️ Some cities have issues. Please check the data files.")

    return all_ready

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
