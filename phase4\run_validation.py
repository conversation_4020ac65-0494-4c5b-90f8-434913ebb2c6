#!/usr/bin/env python3
"""
Phase 4 Validation Script
Runs key validation queries to verify amenity and review data integrity.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

def run_validation():
    """Run validation queries"""
    
    # Database configuration
    db_config = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    print("🔍 PHASE 4 VALIDATION RESULTS")
    print("="*60)
    
    try:
        # Connect to database
        connection = psycopg2.connect(**db_config)
        connection.autocommit = False
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            
            # 1. Overall Amenity Statistics
            print("\n📊 AMENITY STATISTICS")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT a.id) as total_amenities,
                    COUNT(DISTINCT a.category) as unique_categories,
                    COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
                    COUNT(*) as total_amenity_mappings
                FROM amenity a
                LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id
            """)
            
            result = cursor.fetchone()
            print(f"Total amenities: {result['total_amenities']}")
            print(f"Unique categories: {result['unique_categories']}")
            print(f"Accommodations with amenities: {result['accommodations_with_amenities']}")
            print(f"Total amenity mappings: {result['total_amenity_mappings']}")
            
            # 2. Top Amenity Categories
            print("\n📋 TOP AMENITY CATEGORIES")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    a.category,
                    COUNT(DISTINCT a.id) as unique_amenities,
                    COUNT(aaj.id) as total_mappings
                FROM amenity a
                LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id
                GROUP BY a.category
                ORDER BY total_mappings DESC
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                print(f"{row['category']}: {row['unique_amenities']} amenities, {row['total_mappings']} mappings")
            
            # 3. Review Statistics
            print("\n📝 REVIEW STATISTICS")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT r.id) as total_main_reviews,
                    COUNT(DISTINCT r.entity_id) as accommodations_with_reviews,
                    COUNT(DISTINCT re.id) as total_review_entries,
                    COUNT(DISTINCT rar.id) as total_aspect_ratings,
                    AVG(r.overall_rating) as avg_overall_rating
                FROM review r
                LEFT JOIN review_entry re ON r.id = re.review_id
                LEFT JOIN review_aspect_rating rar ON r.id = rar.review_id
                WHERE r.entity_type = 'accommodation'
            """)
            
            result = cursor.fetchone()
            print(f"Main reviews: {result['total_main_reviews']}")
            print(f"Accommodations with reviews: {result['accommodations_with_reviews']}")
            print(f"Individual review entries: {result['total_review_entries']}")
            print(f"Aspect ratings: {result['total_aspect_ratings']}")
            print(f"Average overall rating: {result['avg_overall_rating']:.2f}" if result['avg_overall_rating'] else "N/A")
            
            # 4. Coverage by City
            print("\n🏙️ COVERAGE BY CITY")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    c.name as city_name,
                    COUNT(DISTINCT acc.id) as total_accommodations,
                    COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
                    COUNT(DISTINCT r.entity_id) as accommodations_with_reviews,
                    ROUND(
                        (COUNT(DISTINCT aaj.accommodation_id)::decimal / COUNT(DISTINCT acc.id)) * 100, 
                        1
                    ) as amenity_coverage_pct,
                    ROUND(
                        (COUNT(DISTINCT r.entity_id)::decimal / COUNT(DISTINCT acc.id)) * 100, 
                        1
                    ) as review_coverage_pct
                FROM city c
                JOIN location l ON c.id = l.city_id
                JOIN accommodation acc ON l.id = acc.location_id
                LEFT JOIN accommodation_amenity_junction aaj ON acc.id = aaj.accommodation_id
                LEFT JOIN review r ON acc.id = r.entity_id AND r.entity_type = 'accommodation'
                WHERE c.name IN ('Casablanca', 'Marrakech')
                GROUP BY c.id, c.name
                ORDER BY c.name
            """)
            
            for row in cursor.fetchall():
                print(f"{row['city_name']}:")
                print(f"  Total accommodations: {row['total_accommodations']}")
                print(f"  With amenities: {row['accommodations_with_amenities']} ({row['amenity_coverage_pct']}%)")
                print(f"  With reviews: {row['accommodations_with_reviews']} ({row['review_coverage_pct']}%)")
            
            # 5. Top Amenities
            print("\n⭐ TOP 10 AMENITIES")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    a.name,
                    a.category,
                    COUNT(aaj.id) as mapping_count
                FROM amenity a
                JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id
                GROUP BY a.id, a.name, a.category
                ORDER BY mapping_count DESC
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                print(f"{row['name']} ({row['category']}): {row['mapping_count']} hotels")
            
            # 6. Review Aspects
            print("\n📊 TOP REVIEW ASPECTS")
            print("-" * 30)
            cursor.execute("""
                SELECT 
                    rar.aspect_type,
                    COUNT(*) as aspect_count,
                    AVG(rar.rating_value) as avg_rating
                FROM review_aspect_rating rar
                JOIN review r ON rar.review_id = r.id
                WHERE r.entity_type = 'accommodation'
                GROUP BY rar.aspect_type
                ORDER BY aspect_count DESC
                LIMIT 10
            """)
            
            for row in cursor.fetchall():
                print(f"{row['aspect_type']}: {row['aspect_count']} ratings, avg {row['avg_rating']:.0f}")
            
            # 7. Data Quality Score
            print("\n🎯 DATA QUALITY SCORE")
            print("-" * 30)
            cursor.execute("""
                WITH quality_metrics AS (
                    SELECT 
                        COUNT(DISTINCT acc.id) as total_accommodations,
                        COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
                        COUNT(DISTINCT r.entity_id) as accommodations_with_reviews
                    FROM accommodation acc
                    LEFT JOIN accommodation_amenity_junction aaj ON acc.id = aaj.accommodation_id
                    LEFT JOIN review r ON acc.id = r.entity_id AND r.entity_type = 'accommodation'
                    JOIN location l ON acc.location_id = l.id
                    JOIN city c ON l.city_id = c.id
                    WHERE c.name IN ('Casablanca', 'Marrakech')
                )
                SELECT 
                    total_accommodations,
                    accommodations_with_amenities,
                    accommodations_with_reviews,
                    ROUND((accommodations_with_amenities::decimal / total_accommodations) * 100, 1) as amenity_coverage_pct,
                    ROUND((accommodations_with_reviews::decimal / total_accommodations) * 100, 1) as review_coverage_pct,
                    ROUND(
                        ((accommodations_with_amenities + accommodations_with_reviews)::decimal / (total_accommodations * 2)) * 100, 
                        1
                    ) as overall_quality_score
                FROM quality_metrics
            """)
            
            result = cursor.fetchone()
            print(f"Total accommodations: {result['total_accommodations']}")
            print(f"Amenity coverage: {result['amenity_coverage_pct']}%")
            print(f"Review coverage: {result['review_coverage_pct']}%")
            print(f"Overall quality score: {result['overall_quality_score']}%")
            
        connection.close()
        print("\n✅ Validation completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        return False

def main():
    """Main validation function"""
    success = run_validation()
    
    if success:
        print("\n🎉 Phase 4 data validation passed!")
        print("📈 Amenities and reviews are properly loaded and linked.")
    else:
        print("\n⚠️ Phase 4 data validation issues found.")
        print("🔧 Check the database and data integrity.")
    
    print("="*60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
