#!/usr/bin/env python3
"""
Phase 4: Amenity and Review Relationships Data Loader
Loads amenity and review data from processed JSON files and links them to accommodations using external IDs.
"""

import os
import sys
import json
import logging
import traceback
import psycopg2
from psycopg2.extras import RealDictCursor
from decimal import Decimal
from typing import Dict, List, Optional, Any
from datetime import datetime

# Add the parent directory to the path to import from phase3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DatabaseManager:
    """Database connection and transaction management"""

    def __init__(self, config: Dict):
        self.config = config
        self.connection = None

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.config)
            self.connection.autocommit = False
            logger.info("✓ Database connection established")
            return True
        except Exception as e:
            logger.error(f"✗ Database connection failed: {e}")
            return False

    def get_cursor(self):
        """Get database cursor with RealDictCursor"""
        return self.connection.cursor(cursor_factory=RealDictCursor)

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("✓ Database connection closed")

class Phase4AmenityReviewLoader:
    """Main class for loading amenity and review data"""

    def __init__(self):
        self.db = None
        self.stats = {
            'cities_processed': 0,
            'hotels_processed': 0,
            'amenities_created': 0,
            'amenity_mappings_created': 0,
            'reviews_created': 0,
            'review_entries_created': 0,
            'review_aspects_created': 0,
            'errors': [],
            'city_results': {}
        }

        # Data paths - adjust for running from phase4 directory
        self.processed_data_path = "../processed_data"
        self.amenities_data_path = "../processed_data/amenities"
        self.reviews_data_path = "../processed_data/reviews"

        # Target cities configuration - updated with correct city IDs from database
        self.target_cities = {
            'casablanca': {
                'city_name': 'Casablanca',
                'expected_city_id': 4,
                'processed_file': 'casablanca.json'
            },
            'marrakech': {
                'city_name': 'Marrakech',
                'expected_city_id': 7,
                'processed_file': 'marrakech.json'
            }
        }

    def setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('phase4_amenity_review_loader.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)

    def load_hotel_data(self, city_key: str) -> Dict:
        """Load processed hotel data for a city"""
        try:
            file_path = os.path.join(self.processed_data_path, self.target_cities[city_key]['processed_file'])

            if not os.path.exists(file_path):
                logger.error(f"Processed data file not found: {file_path}")
                return {}

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"✓ Loaded {len(data)} hotels from {file_path}")
                return data

        except Exception as e:
            logger.error(f"Error loading hotel data for {city_key}: {e}")
            return {}

    def load_amenity_data(self, city_key: str, hotel_id: str) -> Optional[Dict]:
        """Load amenity data for a specific hotel"""
        try:
            file_path = os.path.join(self.amenities_data_path, city_key, f"{hotel_id}.json")

            if not os.path.exists(file_path):
                logger.debug(f"Amenity file not found: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        except Exception as e:
            logger.debug(f"Error loading amenity data for {hotel_id}: {e}")
            return None

    def load_review_data(self, city_key: str, hotel_id: str) -> Optional[Dict]:
        """Load review data for a specific hotel"""
        try:
            file_path = os.path.join(self.reviews_data_path, city_key, f"{hotel_id}.json")

            if not os.path.exists(file_path):
                logger.debug(f"Review file not found: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        except Exception as e:
            logger.debug(f"Error loading review data for {hotel_id}: {e}")
            return None

    def get_accommodation_by_external_id(self, external_id: str, city_id: int) -> Optional[int]:
        """Get accommodation ID by external ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT a.id
                    FROM accommodation a
                    JOIN location l ON a.location_id = l.id
                    WHERE a.external_id = %s AND l.city_id = %s
                """, (external_id, city_id))

                result = cursor.fetchone()
                return result['id'] if result else None

        except Exception as e:
            logger.error(f"Error getting accommodation by external ID {external_id}: {e}")
            return None

    def get_or_create_amenity(self, amenity_name: str, category: str) -> Optional[int]:
        """Get existing amenity or create new one"""
        try:
            with self.db.get_cursor() as cursor:
                # First try to find existing amenity by name only (ignore category for duplicates)
                cursor.execute("""
                    SELECT id FROM amenity WHERE name = %s
                """, (amenity_name,))

                result = cursor.fetchone()
                if result:
                    return result['id']

                # Try to create new amenity with ON CONFLICT handling
                cursor.execute("""
                    INSERT INTO amenity (name, category, amenity_type, created_at, updated_at)
                    VALUES (%s, %s, %s, now(), now())
                    ON CONFLICT (name) DO NOTHING
                    RETURNING id
                """, (amenity_name, category, 'accommodation'))

                result = cursor.fetchone()
                if result:
                    logger.debug(f"  ✓ Created new amenity: {amenity_name} ({category})")
                    return result['id']

                # If no result from INSERT (due to conflict), get the existing one
                cursor.execute("""
                    SELECT id FROM amenity WHERE name = %s
                """, (amenity_name,))

                result = cursor.fetchone()
                if result:
                    return result['id']

                return None

        except Exception as e:
            logger.error(f"Error getting/creating amenity {amenity_name}: {e}")
            return None

    def process_hotel_amenities(self, hotel_id: str, accommodation_id: int, city_key: str) -> Dict:
        """Process amenities for a single hotel"""
        result = {'amenities_created': 0, 'mappings_created': 0, 'errors': []}

        try:
            amenity_data = self.load_amenity_data(city_key, hotel_id)
            if not amenity_data:
                return result

            # Navigate the amenity data structure
            hotel_details = amenity_data.get('hotel_details', [])
            if not hotel_details:
                return result

            hotel_info = hotel_details[0]
            amenities_by_group = hotel_info.get('amenities_by_group', {})
            top_amenities = hotel_info.get('top_amenities', {})

            with self.db.get_cursor() as cursor:
                # Process amenities by group
                for group_name, amenities_list in amenities_by_group.items():
                    for amenity_name in amenities_list:
                        try:
                            # Get or create amenity
                            amenity_id = self.get_or_create_amenity(amenity_name, group_name)
                            if not amenity_id:
                                result['errors'].append(f"Failed to create amenity: {amenity_name}")
                                continue

                            # Create amenity mapping with conflict handling
                            cursor.execute("""
                                INSERT INTO accommodation_amenity_junction
                                (accommodation_id, amenity_id, is_available, created_at, updated_at)
                                VALUES (%s, %s, %s, now(), now())
                                ON CONFLICT (accommodation_id, amenity_id) DO NOTHING
                                RETURNING id
                            """, (accommodation_id, amenity_id, True))

                            if cursor.fetchone():
                                result['mappings_created'] += 1
                                logger.debug(f"    ✓ Mapped amenity: {amenity_name}")
                            else:
                                logger.debug(f"    ✓ Amenity mapping already exists: {amenity_name}")

                        except Exception as e:
                            result['errors'].append(f"Error processing amenity {amenity_name}: {e}")
                            logger.debug(f"    ✗ Amenity error: {e}")

                # Process top amenities with additional details
                for amenity_name, amenity_details in top_amenities.items():
                    try:
                        # Get or create amenity
                        amenity_id = self.get_or_create_amenity(amenity_name, "Top amenities")
                        if not amenity_id:
                            result['errors'].append(f"Failed to create top amenity: {amenity_name}")
                            continue

                        # Create amenity mapping with additional details and conflict handling
                        is_free = amenity_details.get('is_free', False)
                        is_available = amenity_details.get('is_available', True)

                        cursor.execute("""
                            INSERT INTO accommodation_amenity_junction
                            (accommodation_id, amenity_id, is_available, is_free, is_top_amenity, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, now(), now())
                            ON CONFLICT (accommodation_id, amenity_id) DO NOTHING
                            RETURNING id
                        """, (accommodation_id, amenity_id, is_available, is_free, True))

                        if cursor.fetchone():
                            result['mappings_created'] += 1
                            logger.debug(f"    ✓ Mapped top amenity: {amenity_name}")
                        else:
                            logger.debug(f"    ✓ Top amenity mapping already exists: {amenity_name}")

                    except Exception as e:
                        result['errors'].append(f"Error processing top amenity {amenity_name}: {e}")
                        logger.debug(f"    ✗ Top amenity error: {e}")

        except Exception as e:
            result['errors'].append(f"Amenity processing error: {e}")
            logger.error(f"  ✗ Amenity processing error for hotel {hotel_id}: {e}")

        return result

    def process_hotel_reviews(self, hotel_id: str, accommodation_id: int, city_key: str) -> Dict:
        """Process reviews for a single hotel using proper V7 schema structure"""
        result = {'reviews_created': 0, 'review_entries_created': 0, 'review_aspects_created': 0, 'errors': []}

        try:
            review_data = self.load_review_data(city_key, hotel_id)
            if not review_data:
                return result

            with self.db.get_cursor() as cursor:
                # Process overall rating information - create main review record
                overall_rating = review_data.get('rating')
                rating_count = review_data.get('ratingCount')
                formatted_rating = review_data.get('formattedRating')
                language_tag = review_data.get('reviewRatings', {}).get('languageTag', 'en')

                review_id = None

                if overall_rating and rating_count:
                    # Check if review summary already exists
                    cursor.execute("""
                        SELECT id FROM review
                        WHERE entity_type = 'accommodation' AND entity_id = %s
                    """, (accommodation_id,))

                    existing_review = cursor.fetchone()

                    if not existing_review:
                        # Create main review record
                        cursor.execute("""
                            INSERT INTO review (
                                entity_type, entity_id, overall_rating, formatted_rating,
                                review_count, language_tag, created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, now(), now())
                            RETURNING id
                        """, (
                            'accommodation', accommodation_id, overall_rating,
                            formatted_rating, rating_count, language_tag
                        ))

                        review_result = cursor.fetchone()
                        if review_result:
                            review_id = review_result['id']
                            result['reviews_created'] += 1
                            logger.debug(f"    ✓ Created main review: {formatted_rating} ({rating_count} reviews)")
                    else:
                        review_id = existing_review['id']
                        logger.debug(f"    ✓ Using existing review record: {review_id}")

                # Process individual review entries
                if review_id:
                    review_ratings = review_data.get('reviewRatings', {})
                    individual_reviews = review_ratings.get('reviews', [])

                    for review_entry in individual_reviews:
                        try:
                            review_text = review_entry.get('text')
                            review_rating = review_entry.get('rating')
                            author = review_entry.get('author')
                            travelled_at = review_entry.get('travelledAt')
                            created_at = review_entry.get('createdAt')
                            advertiser = review_entry.get('advertiser')

                            if not review_text:
                                continue

                            # Parse dates
                            travelled_date = None
                            created_date = None

                            if travelled_at:
                                try:
                                    travelled_date = datetime.fromisoformat(travelled_at.replace('Z', '+00:00'))
                                except:
                                    pass

                            if created_at:
                                try:
                                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                except:
                                    pass

                            # Create individual review entry
                            cursor.execute("""
                                INSERT INTO review_entry (
                                    review_id, review_text, rating, author,
                                    travelled_at, created_at, advertiser
                                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                                RETURNING id
                            """, (
                                review_id, review_text, review_rating, author,
                                travelled_date, created_date, advertiser
                            ))

                            entry_result = cursor.fetchone()
                            if entry_result:
                                result['review_entries_created'] += 1
                                logger.debug(f"    ✓ Created review entry")

                        except Exception as e:
                            result['errors'].append(f"Error processing individual review: {e}")
                            logger.debug(f"    ✗ Individual review error: {e}")

                    # Process aspect ratings
                    aspect_ratings = review_data.get('aspectRatings', [])
                    for aspect in aspect_ratings:
                        try:
                            aspect_name = aspect.get('type_of_aspect')
                            aspect_rating = aspect.get('value')

                            if aspect_name and aspect_rating:
                                # Create aspect rating entry
                                cursor.execute("""
                                    INSERT INTO review_aspect_rating (
                                        review_id, aspect_type, rating_value, created_at
                                    ) VALUES (%s, %s, %s, now())
                                    RETURNING id
                                """, (review_id, aspect_name, aspect_rating))

                                aspect_result = cursor.fetchone()
                                if aspect_result:
                                    result['review_aspects_created'] += 1
                                    logger.debug(f"    ✓ Created aspect rating: {aspect_name}")

                        except Exception as e:
                            result['errors'].append(f"Error processing aspect rating: {e}")
                            logger.debug(f"    ✗ Aspect rating error: {e}")

                    # Process advertiser ratings as additional aspect ratings
                    advertiser_ratings = review_data.get('advertiserRatings', [])
                    for advertiser_rating in advertiser_ratings:
                        try:
                            advertiser = advertiser_rating.get('advertiser')
                            rating_value = advertiser_rating.get('value')

                            if advertiser and rating_value:
                                # Create advertiser rating as aspect rating
                                cursor.execute("""
                                    INSERT INTO review_aspect_rating (
                                        review_id, aspect_type, rating_value, created_at
                                    ) VALUES (%s, %s, %s, now())
                                    RETURNING id
                                """, (review_id, f"{advertiser}_Rating", rating_value))

                                advertiser_result = cursor.fetchone()
                                if advertiser_result:
                                    result['review_aspects_created'] += 1
                                    logger.debug(f"    ✓ Created advertiser rating: {advertiser}")

                        except Exception as e:
                            result['errors'].append(f"Error processing advertiser rating: {e}")
                            logger.debug(f"    ✗ Advertiser rating error: {e}")

        except Exception as e:
            result['errors'].append(f"Review processing error: {e}")
            logger.error(f"  ✗ Review processing error for hotel {hotel_id}: {e}")

        return result

    def process_single_hotel(self, hotel_data: Dict, accommodation_id: int, city_key: str) -> Dict:
        """Process amenities and reviews for a single hotel"""
        result = {
            'success': False,
            'amenities_created': 0,
            'amenity_mappings_created': 0,
            'reviews_created': 0,
            'review_entries_created': 0,
            'review_aspects_created': 0,
            'error': None
        }

        try:
            hotel_id = str(hotel_data.get('hotel_id'))
            hotel_name = hotel_data.get('hotel_details', {}).get('name', 'Unknown')

            logger.debug(f"  Processing amenities and reviews for: {hotel_name} (ID: {hotel_id})")

            # Process amenities
            amenity_result = self.process_hotel_amenities(hotel_id, accommodation_id, city_key)
            result['amenities_created'] = amenity_result['amenities_created']
            result['amenity_mappings_created'] = amenity_result['mappings_created']

            # Process reviews
            review_result = self.process_hotel_reviews(hotel_id, accommodation_id, city_key)
            result['reviews_created'] = review_result['reviews_created']
            result['review_entries_created'] = review_result['review_entries_created']
            result['review_aspects_created'] = review_result['review_aspects_created']

            # Collect errors
            all_errors = amenity_result['errors'] + review_result['errors']
            if all_errors:
                result['error'] = f"Partial errors: {'; '.join(all_errors[:3])}"  # Show first 3 errors

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"  ✗ Hotel processing error: {e}")
            logger.error(traceback.format_exc())

        return result

    def process_city(self, city_key: str) -> Dict:
        """Process all hotels in a city"""
        city_config = self.target_cities[city_key]
        city_stats = {
            'city_name': city_config['city_name'],
            'hotels_found': 0,
            'hotels_processed': 0,
            'amenities_created': 0,
            'amenity_mappings_created': 0,
            'reviews_created': 0,
            'review_entries_created': 0,
            'review_aspects_created': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            logger.info("="*60)
            logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
            logger.info("="*60)

            # Load hotel data for this city
            hotels_data = self.load_hotel_data(city_key)
            if not hotels_data:
                city_stats['errors'].append(f"No hotel data found for {city_config['city_name']}")
                city_stats['status'] = 'failed'
                return city_stats

            city_stats['hotels_found'] = len(hotels_data)
            logger.info(f"Found {city_stats['hotels_found']} hotels in {city_config['city_name']}")

            # Process each hotel
            for hotel_index, (external_id, hotel_data) in enumerate(hotels_data.items()):
                try:
                    hotel_name = hotel_data.get('hotel_details', {}).get('name', 'Unknown')
                    logger.info(f"Processing hotel {hotel_index + 1}/{city_stats['hotels_found']}: {hotel_name}")

                    # Get accommodation ID using external ID
                    accommodation_id = self.get_accommodation_by_external_id(
                        external_id,
                        city_config['expected_city_id']
                    )

                    if not accommodation_id:
                        error_msg = f"Hotel {hotel_index + 1}: No accommodation found for external ID {external_id}"
                        city_stats['errors'].append(error_msg)
                        logger.warning(f"  ⚠ {error_msg}")
                        continue

                    # Process this hotel's amenities and reviews
                    result = self.process_single_hotel(hotel_data, accommodation_id, city_key)

                    if result['success']:
                        # Commit this individual hotel
                        self.db.connection.commit()
                        city_stats['hotels_processed'] += 1
                        city_stats['amenities_created'] += result['amenities_created']
                        city_stats['amenity_mappings_created'] += result['amenity_mappings_created']
                        city_stats['reviews_created'] += result['reviews_created']
                        city_stats['review_entries_created'] += result['review_entries_created']
                        city_stats['review_aspects_created'] += result['review_aspects_created']
                        logger.info(f"✓ Hotel processed and committed successfully")
                    else:
                        # Rollback this individual hotel but continue with others
                        self.db.connection.rollback()
                        city_stats['errors'].append(f"Hotel {hotel_index + 1}: {result['error']}")
                        logger.error(f"✗ Hotel processing failed: {result['error']}")

                except Exception as e:
                    # Rollback this individual hotel but continue with others
                    self.db.connection.rollback()
                    error_msg = f"Hotel {hotel_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    logger.error(traceback.format_exc())
                    city_stats['errors'].append(error_msg)

                # Progress update every 50 hotels
                if (hotel_index + 1) % 50 == 0:
                    logger.info(f"  Progress: {hotel_index + 1}/{city_stats['hotels_found']} hotels processed")

            city_stats['status'] = 'completed'
            logger.info(f"✓ {city_config['city_name']} processing completed")
            logger.info(f"  Hotels processed: {city_stats['hotels_processed']}/{city_stats['hotels_found']}")
            logger.info(f"  Amenity mappings created: {city_stats['amenity_mappings_created']}")
            logger.info(f"  Reviews created: {city_stats['reviews_created']}")
            logger.info(f"  Review entries created: {city_stats['review_entries_created']}")
            logger.info(f"  Errors: {len(city_stats['errors'])}")

        except Exception as e:
            # City-level error
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def run(self):
        """Main execution method"""
        global logger
        logger = self.setup_logging()

        logger.info("="*80)
        logger.info("PHASE 4: AMENITY AND REVIEW RELATIONSHIPS DATA LOADER")
        logger.info("="*80)

        try:
            # Database configuration - matches Phase 3 configuration
            db_config = {
                'host': 'localhost',
                'database': 'testing_full_database',
                'user': 'postgres',
                'password': '1234',
                'port': 5432
            }

            # Initialize database connection
            self.db = DatabaseManager(db_config)
            if not self.db.connect():
                logger.error("Failed to connect to database. Exiting.")
                return False

            # Process each target city
            for city_key in self.target_cities.keys():
                city_result = self.process_city(city_key)
                self.stats['city_results'][city_key] = city_result

                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['hotels_processed'] += city_result['hotels_processed']
                    self.stats['amenities_created'] += city_result['amenities_created']
                    self.stats['amenity_mappings_created'] += city_result['amenity_mappings_created']
                    self.stats['reviews_created'] += city_result['reviews_created']
                    self.stats['review_entries_created'] += city_result['review_entries_created']
                    self.stats['review_aspects_created'] += city_result['review_aspects_created']

                # Collect errors
                self.stats['errors'].extend(city_result['errors'])

            # Print final summary
            self.print_final_summary()

            return True

        except Exception as e:
            logger.error(f"Critical error in main execution: {e}")
            logger.error(traceback.format_exc())
            return False

        finally:
            if self.db:
                self.db.close()

    def print_final_summary(self):
        """Print comprehensive final summary"""
        logger.info("="*80)
        logger.info("PHASE 4 EXECUTION SUMMARY")
        logger.info("="*80)

        logger.info(f"Cities processed: {self.stats['cities_processed']}/{len(self.target_cities)}")
        logger.info(f"Total hotels processed: {self.stats['hotels_processed']}")
        logger.info(f"Total amenities created: {self.stats['amenities_created']}")
        logger.info(f"Total amenity mappings created: {self.stats['amenity_mappings_created']}")
        logger.info(f"Total reviews created: {self.stats['reviews_created']}")
        logger.info(f"Total review entries created: {self.stats['review_entries_created']}")
        logger.info(f"Total review aspects created: {self.stats['review_aspects_created']}")
        logger.info(f"Total errors: {len(self.stats['errors'])}")

        # City-by-city breakdown
        logger.info("\nCITY-BY-CITY BREAKDOWN:")
        logger.info("-" * 40)

        for city_key, city_result in self.stats['city_results'].items():
            city_name = city_result['city_name']
            status = city_result['status']

            logger.info(f"\n{city_name}:")
            logger.info(f"  Status: {status}")
            logger.info(f"  Hotels processed: {city_result['hotels_processed']}/{city_result.get('hotels_found', 0)}")
            logger.info(f"  Amenity mappings: {city_result['amenity_mappings_created']}")
            logger.info(f"  Reviews: {city_result['reviews_created']}")
            logger.info(f"  Review entries: {city_result['review_entries_created']}")
            logger.info(f"  Review aspects: {city_result['review_aspects_created']}")
            logger.info(f"  Errors: {len(city_result['errors'])}")

            if city_result['errors']:
                logger.info(f"  Sample errors:")
                for error in city_result['errors'][:3]:  # Show first 3 errors
                    logger.info(f"    - {error}")

        # Overall success assessment
        total_expected_cities = len(self.target_cities)
        success_rate = (self.stats['cities_processed'] / total_expected_cities) * 100 if total_expected_cities > 0 else 0

        logger.info(f"\nOVERALL SUCCESS RATE: {success_rate:.1f}%")

        if success_rate == 100:
            logger.info("🎉 Phase 4 completed successfully! All amenities and reviews loaded.")
        elif success_rate >= 80:
            logger.info("✅ Phase 4 mostly successful with some issues.")
        else:
            logger.info("⚠️  Phase 4 completed with significant issues.")

        logger.info("="*80)


def main():
    """Main entry point"""
    loader = Phase4AmenityReviewLoader()
    success = loader.run()

    if success:
        print("\n✅ Phase 4 amenity and review loading completed successfully!")
        print("📊 Check the log file 'phase4_amenity_review_loader.log' for detailed results.")
        print("🔍 Run the validation queries to verify the data integrity.")
    else:
        print("\n❌ Phase 4 amenity and review loading failed!")
        print("📋 Check the log file for error details.")

    return success


if __name__ == "__main__":
    main()