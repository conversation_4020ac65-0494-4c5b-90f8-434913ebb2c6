#!/usr/bin/env python3
"""
Phase 5 Database Connection Test
Quick test to verify database connection and schema before running the contact loader.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os

def test_database_connection():
    """Test database connection and basic schema for Phase 5"""
    print("🔍 Testing Database Connection for Phase 5...")
    
    # Database configuration - matches previous phases
    db_config = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    try:
        # Test connection
        connection = psycopg2.connect(**db_config)
        connection.autocommit = False
        print("✅ Database connection successful")
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            # Test required tables exist
            required_tables = [
                'accommodation',
                'location', 
                'city'
            ]
            
            print("\n🔍 Checking required tables...")
            for table in required_tables:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    );
                """, (table,))
                
                exists = cursor.fetchone()['exists']
                status = "✅" if exists else "❌"
                print(f"  {status} {table}")
                
                if not exists:
                    print(f"    ❌ Table {table} is missing!")
                    return False
            
            # Test accommodation table has required contact fields
            print("\n🔍 Checking accommodation table contact fields...")
            required_columns = ['phone', 'website', 'external_id']
            
            for column in required_columns:
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'accommodation' 
                    AND column_name = %s
                """, (column,))
                
                column_exists = cursor.fetchone() is not None
                status = "✅" if column_exists else "❌"
                print(f"  {status} accommodation.{column}")
                
                if not column_exists:
                    print(f"    ❌ accommodation.{column} column is missing!")
                    return False
            
            # Test location table has address field
            print("\n🔍 Checking location table address field...")
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'location' 
                AND column_name = 'address'
            """)
            
            address_exists = cursor.fetchone() is not None
            status = "✅" if address_exists else "❌"
            print(f"  {status} location.address")
            
            if not address_exists:
                print("    ❌ location.address column is missing!")
                return False
            
            # Test sample data exists
            print("\n🔍 Checking sample data...")
            
            # Check accommodations
            cursor.execute("SELECT COUNT(*) as count FROM accommodation")
            acc_count = cursor.fetchone()['count']
            print(f"  ✅ {acc_count} accommodations found")
            
            if acc_count == 0:
                print("    ❌ No accommodations found! Run Phase 3 first.")
                return False
            
            # Check accommodations with external_id
            cursor.execute("SELECT COUNT(*) as count FROM accommodation WHERE external_id IS NOT NULL")
            ext_id_count = cursor.fetchone()['count']
            print(f"  ✅ {ext_id_count} accommodations with external_id")
            
            if ext_id_count == 0:
                print("    ❌ No accommodations with external_id found! Run Phase 3 first.")
                return False
            
            # Check cities
            cursor.execute("SELECT id, name FROM city WHERE name IN ('Casablanca', 'Marrakech') ORDER BY name")
            cities = cursor.fetchall()
            print(f"  ✅ Target cities found:")
            for city in cities:
                print(f"    - {city['name']} (ID: {city['id']})")
            
            expected_cities = ['Casablanca', 'Marrakech']
            found_cities = [city['name'] for city in cities]
            
            for expected_city in expected_cities:
                if expected_city not in found_cities:
                    print(f"    ❌ Expected city {expected_city} not found!")
                    return False
            
            # Check current contact data coverage (before Phase 5)
            print("\n📊 Current contact data coverage (before Phase 5):")
            cursor.execute("""
                SELECT 
                    c.name as city_name,
                    COUNT(*) as total_accommodations,
                    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as with_phone,
                    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as with_website,
                    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address
                FROM city c
                JOIN location l ON c.id = l.city_id
                JOIN accommodation acc ON l.id = acc.location_id
                WHERE c.name IN ('Casablanca', 'Marrakech')
                GROUP BY c.id, c.name
                ORDER BY c.name
            """)
            
            for row in cursor.fetchall():
                print(f"  {row['city_name']}:")
                print(f"    Total accommodations: {row['total_accommodations']}")
                print(f"    With phone: {row['with_phone']}")
                print(f"    With website: {row['with_website']}")
                print(f"    With address: {row['with_address']}")
        
        connection.close()
        
        # Check contact data files exist
        print("\n🔍 Checking contact data files...")
        contact_files = [
            '../processed_data/contacts/casablanca.json',
            '../processed_data/contacts/marrakech.json'
        ]
        
        for file_path in contact_files:
            if os.path.exists(file_path):
                print(f"  ✅ {file_path}")
                # Check file size
                file_size = os.path.getsize(file_path)
                print(f"    File size: {file_size:,} bytes")
            else:
                print(f"  ❌ {file_path} - File not found!")
                return False
        
        print("\n🎉 All database tests passed! Ready to run Phase 5 contact loader.")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    """Main test function"""
    print("="*60)
    print("PHASE 5 DATABASE CONNECTION TEST")
    print("="*60)
    
    success = test_database_connection()
    
    if success:
        print("\n✅ Database is ready for Phase 5!")
        print("🚀 You can now run: python phase5_contact_loader.py")
    else:
        print("\n❌ Database is not ready for Phase 5!")
        print("🔧 Please fix the issues above before running the loader.")
    
    print("="*60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
