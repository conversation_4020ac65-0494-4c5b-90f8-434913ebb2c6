# Hotel Data Loading Implementation Guide

This guide provides complete instructions for implementing the comprehensive hotel data loading system for your V5 database schema.

## Files Provided

1. **`pgadmin_schema_modifications.sql`** - PostgreSQL schema modifications
2. **`phase2_reference_data_loader.py`** - Python implementation for Phase 2 data loading
3. **`requirements.txt`** - Python dependencies

## Prerequisites

- PostgreSQL database with V5 schema
- Python 3.8 or higher
- pgAdmin or PostgreSQL command line access
- Backup of your database (CRITICAL - create backup before proceeding)

## Step 1: Database Schema Modifications

### Execute in pgAdmin

1. Open pgAdmin and connect to your database
2. Open the Query Tool
3. Copy and paste the entire contents of `pgadmin_schema_modifications.sql`
4. Execute the script

**What this does:**
- Creates new tables: `review`, `review_entry`, `review_aspect_rating`, `accommodation_price_forecast`
- Adds new columns to `accommodation`: `construction_year`, `highlights`
- Extends `phone` field to handle international numbers
- Adds amenity pricing columns: `is_free`, `is_available`, `is_top_amenity`
- Creates all necessary indexes and foreign key constraints
- Ensures "Hotel" accommodation type exists

### Verification

After execution, verify the changes:

```sql
-- Check new tables exist
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('review', 'review_entry', 'review_aspect_rating', 'accommodation_price_forecast');

-- Check new accommodation columns
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'accommodation'
AND column_name IN ('construction_year', 'highlights');

-- Check amenity junction columns
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'accommodation_amenity_junction'
AND column_name IN ('is_free', 'is_available', 'is_top_amenity');

-- Check Hotel accommodation type exists
SELECT * FROM accommodation_type WHERE name = 'Hotel';
```

## Step 2: Python Environment Setup

### Install Dependencies

```bash
pip install psycopg2-binary
```

Or if you prefer using a requirements file:

```bash
pip install -r requirements.txt
```

### Configure Database Connection

Edit the `DB_CONFIG` section in `phase2_reference_data_loader.py`:

```python
DB_CONFIG = {
    'host': 'localhost',          # Your PostgreSQL host
    'database': 'your_db_name',   # Your database name
    'user': 'your_username',      # Your PostgreSQL username
    'password': 'your_password',  # Your PostgreSQL password
    'port': 5432                  # Your PostgreSQL port
}
```

### Set Data Directory Path

Update the `DATA_DIRECTORY` variable:

```python
DATA_DIRECTORY = "processed_data"  # Path to your JSON data files
```

## Step 3: Execute Phase 2 Reference Data Loading

### Test the Fixes (Optional)

Before running the full Phase 2, you can test the fixes:

```bash
python test_phase2_fixes.py
```

This will verify that amenity extraction and city mapping work correctly.

### Run the Script

```bash
python phase2_reference_data_loader.py
```

### What Phase 2 Does

1. **JSON File Validation**
   - Validates all JSON files are readable and well-formed
   - Checks main hotel files and subdirectory files (reviews, amenities, contacts)
   - Reports any corrupted or invalid files

2. **City-Filename Mapping**
   - Maps JSON filenames to existing city records in your database
   - Uses intelligent matching (exact and partial)
   - Creates mapping for data loading phases

3. **Amenity Extraction and Normalization**
   - Extracts all unique amenities from JSON files
   - Applies intelligent normalization (WiFi, Free WiFi → WiFi)
   - Categorizes amenities (Internet & Technology, Recreation, etc.)

4. **Database Loading**
   - Loads normalized amenities into the `amenity` table
   - Avoids duplicates by checking existing amenities
   - Creates amenity ID mapping for future phases

### Expected Output

```
============================================================
PHASE 2 REFERENCE DATA LOADING RESULTS
============================================================
Status: success
Files validated: 156
Files invalid: 0
Cities mapped: 12
Amenities processed: 87

City mappings created: 12
Amenity mappings created: 87

Phase 2 completed successfully!
```

### Logging

The script creates detailed logs in `phase2_loading.log` with:
- Detailed progress information
- Error messages and stack traces
- Database operations
- File processing status

## Data Quality Considerations

### Amenity Normalization

The system includes comprehensive amenity normalization:

- **WiFi Variations**: "WiFi", "Free WiFi", "WiFi in lobby" → "WiFi"
- **Parking Variations**: "Parking", "Free parking", "Valet parking" → "Parking"
- **Pool Variations**: "Pool", "Indoor pool", "Swimming pool" → "Pool"

### City Mapping Strategy

- **Exact Match**: `casablanca.json` → City named "Casablanca"
- **Partial Match**: `marrakech.json` → City containing "marrakech"
- **Case Insensitive**: All matching is case-insensitive

### Error Handling

- **Database Transactions**: All operations use proper transaction management
- **Rollback on Error**: Failed operations are rolled back automatically
- **Comprehensive Logging**: All errors are logged with full context
- **Graceful Degradation**: Individual file failures don't stop the entire process

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify database credentials in `DB_CONFIG`
   - Check PostgreSQL is running and accessible
   - Verify database name exists

2. **Schema Modification Errors**
   - Ensure you have sufficient database privileges
   - Check if tables already exist (script handles this)
   - Verify PostgreSQL version compatibility

3. **JSON File Validation Failures**
   - Check file encoding (should be UTF-8)
   - Verify JSON syntax is valid
   - Ensure files are not corrupted

4. **City Mapping Issues**
   - Verify cities exist in database with country_id = 1 (Morocco)
   - Check city names match JSON filenames
   - Review partial matching logic in logs

### Specific Issues and Fixes

#### Issue: "Amenities processed: 0"

**Problem**: The original code expected amenities at the root level of JSON files, but they're nested inside `hotel_details` array.

**Solution**: Updated `extract_all_amenities()` method to handle the correct JSON structure:
```python
# Handle the nested structure: hotel_details array
hotel_details_list = amenity_data.get('hotel_details', [])
for hotel_details in hotel_details_list:
    # Process amenities_by_group and top_amenities
```

#### Issue: "No city mapping found for fes.json"

**Problem**: Database has city named "Fez" but JSON file is named "fes.json".

**Solution**: Added city name variations mapping:
```python
city_variations = {
    'fes': 'fez',  # fes.json should map to Fez city
    'fès': 'fez',  # Alternative spelling
}
```

#### Issue: "No city mapping found for nador.json"

**Problem**: Nador city doesn't exist in your database.

**Solution**: Added logic to skip known missing cities without generating errors:
```python
if filename not in ['nador']:  # Known files to skip
    self.stats['errors'].append(f"No city mapping found for {json_file.name}")
else:
    logger.info(f"Skipping {json_file.name} - city not in database (expected)")
```

### Performance Considerations

- **Batch Processing**: Amenities are processed in batches for efficiency
- **Index Creation**: All necessary indexes are created for optimal performance
- **Memory Usage**: Large JSON files are processed one at a time
- **Database Connections**: Proper connection pooling and cleanup

## Next Steps

After successful Phase 2 completion:

1. **Phase 3**: Core hotel data loading (locations, accommodations, images)
2. **Phase 4**: Relationship data loading (amenities, reviews, contacts)
3. **Phase 5**: Validation and cleanup

## Security Notes

- **Database Credentials**: Never commit database credentials to version control
- **Backup Strategy**: Always maintain current database backups
- **Access Control**: Ensure appropriate database user permissions
- **Data Validation**: All input data is validated before database insertion

## Support

For issues or questions:
1. Check the detailed logs in `phase2_loading.log`
2. Review error messages in the console output
3. Verify database schema modifications were applied correctly
4. Test with a small subset of data first
