#!/usr/bin/env python3
"""
Phase 2: Reference Data Loading for Hotel Data Import
Loads and normalizes amenities, creates city-filename mapping, validates JSON files
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql
import traceback
from config import db_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase2_loading.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnection:
    """Manages PostgreSQL database connections with proper error handling"""

    def __init__(self, host: str, database: str, user: str, password: str, port: int = 5432):
        self.connection_params = {
            'host': host,
            'database': database,
            'user': user,
            'password': password,
            'port': port
        }
        self.connection = None

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.connection_params)
            self.connection.autocommit = False
            logger.info("Database connection established successfully")
            return self.connection
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")

    def get_cursor(self):
        """Get database cursor with RealDictCursor for named access"""
        if not self.connection:
            self.connect()
        return self.connection.cursor(cursor_factory=RealDictCursor)

class AmenityNormalizer:
    """Handles amenity normalization and categorization"""

    # Comprehensive amenity normalization mapping
    AMENITY_NORMALIZATION = {
        'WiFi': {
            'variations': ['WiFi', 'Free WiFi', 'WiFi in lobby', 'WiFi in rooms', 'Wireless Internet', 'Internet', 'Free internet'],
            'category': 'Internet & Technology'
        },
        'Parking': {
            'variations': ['Parking', 'Free parking', 'Paid parking', 'Valet parking', 'Private parking', 'On-site parking'],
            'category': 'Transportation'
        },
        'Pool': {
            'variations': ['Pool', 'Indoor pool', 'Outdoor pool', 'Swimming pool', 'Indoor Pool and Terrace'],
            'category': 'Recreation'
        },
        'Fitness Center': {
            'variations': ['Fitness center', 'Gym', 'Fitness centre', 'Exercise room', 'Fitness facilities'],
            'category': 'Health & Fitness'
        },
        'Spa': {
            'variations': ['Spa', 'Wellness center', 'Massage', 'Beauty salon', 'Sauna', 'Thalasso Therapy Spa Experience'],
            'category': 'Wellness / Spa'
        },
        'Restaurant': {
            'variations': ['Restaurant', 'On-site restaurant', 'Dining', 'Restaurant and Bar', 'On-site Restaurant and Coffee Shop'],
            'category': 'Dining'
        },
        'Bar': {
            'variations': ['Bar', 'Lounge', 'Cocktail bar', 'Halal Cuisine and Bar'],
            'category': 'Dining'
        },
        'Room Service': {
            'variations': ['Room service', '24-hour room service', 'In-room dining'],
            'category': 'Room Services'
        },
        'Air Conditioning': {
            'variations': ['Air conditioning', 'AC', 'Climate control', 'Heating'],
            'category': 'Room Amenities'
        },
        'Business Center': {
            'variations': ['Business center', 'Business centre', 'Meeting rooms', 'Conference facilities', 'Event Facilities and Meeting Spaces'],
            'category': 'Business Services'
        },
        'Pet Friendly': {
            'variations': ['Pet-friendly', 'Pets allowed', 'Pet-Friendly Accommodation'],
            'category': 'Policies'
        },
        'Concierge': {
            'variations': ['Concierge', 'Concierge service', 'Guest services'],
            'category': 'Guest Services'
        },
        'Laundry': {
            'variations': ['Laundry', 'Laundry service', 'Dry cleaning'],
            'category': 'Services'
        }
    }

    @classmethod
    def normalize_amenity_name(cls, amenity_name: str) -> Tuple[str, str]:
        """
        Normalize amenity name and return (normalized_name, category)
        """
        amenity_name = amenity_name.strip()

        # Check for exact matches in normalization mapping
        for normalized_name, config in cls.AMENITY_NORMALIZATION.items():
            if amenity_name in config['variations']:
                return normalized_name, config['category']

        # If no match found, try partial matching
        amenity_lower = amenity_name.lower()
        for normalized_name, config in cls.AMENITY_NORMALIZATION.items():
            for variation in config['variations']:
                if variation.lower() in amenity_lower or amenity_lower in variation.lower():
                    return normalized_name, config['category']

        # If still no match, categorize based on keywords
        if any(word in amenity_lower for word in ['wifi', 'internet', 'wireless']):
            return amenity_name, 'Internet & Technology'
        elif any(word in amenity_lower for word in ['pool', 'swimming']):
            return amenity_name, 'Recreation'
        elif any(word in amenity_lower for word in ['spa', 'massage', 'wellness']):
            return amenity_name, 'Wellness / Spa'
        elif any(word in amenity_lower for word in ['restaurant', 'dining', 'food']):
            return amenity_name, 'Dining'
        elif any(word in amenity_lower for word in ['parking', 'valet']):
            return amenity_name, 'Transportation'
        elif any(word in amenity_lower for word in ['business', 'meeting', 'conference']):
            return amenity_name, 'Business Services'
        else:
            return amenity_name, 'General'

class Phase2ReferenceDataLoader:
    """Main class for Phase 2 reference data loading"""

    def __init__(self, db_connection: DatabaseConnection, data_directory: str):
        self.db = db_connection
        self.data_dir = Path(data_directory)
        self.amenity_normalizer = AmenityNormalizer()
        self.stats = {
            'files_validated': 0,
            'files_invalid': 0,
            'amenities_processed': 0,
            'cities_mapped': 0,
            'errors': []
        }

    def validate_json_files(self) -> Dict[str, List[str]]:
        """
        Validate all JSON files are readable and well-formed
        Returns dict with 'valid' and 'invalid' file lists
        """
        logger.info("Starting JSON file validation...")

        valid_files = []
        invalid_files = []

        # Check main hotel data files
        for json_file in self.data_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and len(data) > 0:
                        valid_files.append(str(json_file))
                        self.stats['files_validated'] += 1
                    else:
                        invalid_files.append(str(json_file))
                        self.stats['files_invalid'] += 1
                        logger.warning(f"Empty or invalid structure in {json_file}")
            except Exception as e:
                invalid_files.append(str(json_file))
                self.stats['files_invalid'] += 1
                self.stats['errors'].append(f"JSON validation error in {json_file}: {e}")
                logger.error(f"Failed to validate {json_file}: {e}")

        # Check subdirectory files (reviews, amenities, contacts)
        for subdir in ['reviews', 'amenities', 'contacts']:
            subdir_path = self.data_dir / subdir
            if subdir_path.exists():
                for json_file in subdir_path.rglob("*.json"):
                    try:
                        with open(json_file, 'r', encoding='utf-8') as f:
                            json.load(f)
                        valid_files.append(str(json_file))
                        self.stats['files_validated'] += 1
                    except Exception as e:
                        invalid_files.append(str(json_file))
                        self.stats['files_invalid'] += 1
                        self.stats['errors'].append(f"JSON validation error in {json_file}: {e}")
                        logger.error(f"Failed to validate {json_file}: {e}")

        logger.info(f"JSON validation complete: {len(valid_files)} valid, {len(invalid_files)} invalid")
        return {'valid': valid_files, 'invalid': invalid_files}

    def create_city_filename_mapping(self) -> Dict[str, int]:
        """
        Create mapping between JSON filenames and existing city IDs
        Returns dict: {filename: city_id}
        """
        logger.info("Creating city-filename mapping...")

        city_mapping = {}

        try:
            with self.db.get_cursor() as cursor:
                # Get all cities in Morocco (country_id = 1)
                cursor.execute("""
                    SELECT id, name, LOWER(name) as name_lower
                    FROM city
                    WHERE country_id = 1
                """)
                cities = cursor.fetchall()

                # Create lookup dictionary
                city_lookup = {city['name_lower']: city['id'] for city in cities}

                # Map JSON files to cities
                for json_file in self.data_dir.glob("*.json"):
                    filename = json_file.stem.lower()  # Remove .json extension and lowercase

                    if filename in city_lookup:
                        city_mapping[json_file.name] = city_lookup[filename]
                        self.stats['cities_mapped'] += 1
                        logger.info(f"Mapped {json_file.name} to city ID {city_lookup[filename]}")
                    else:
                        # Try partial matching
                        for city_name, city_id in city_lookup.items():
                            if filename in city_name or city_name in filename:
                                city_mapping[json_file.name] = city_id
                                self.stats['cities_mapped'] += 1
                                logger.info(f"Mapped {json_file.name} to city ID {city_id} (partial match)")
                                break
                        else:
                            self.stats['errors'].append(f"No city mapping found for {json_file.name}")
                            logger.warning(f"No city mapping found for {json_file.name}")

        except Exception as e:
            self.stats['errors'].append(f"City mapping error: {e}")
            logger.error(f"Failed to create city mapping: {e}")
            raise

        logger.info(f"City mapping complete: {len(city_mapping)} files mapped")
        return city_mapping

    def extract_all_amenities(self) -> Set[Tuple[str, str]]:
        """
        Extract all unique amenities from JSON files
        Returns set of (normalized_name, category) tuples
        """
        logger.info("Extracting amenities from JSON files...")

        all_amenities = set()

        # Process amenities from amenities directory
        amenities_dir = self.data_dir / 'amenities'
        if amenities_dir.exists():
            for amenity_file in amenities_dir.rglob("*.json"):
                try:
                    with open(amenity_file, 'r', encoding='utf-8') as f:
                        amenity_data = json.load(f)

                    # Process grouped amenities
                    if 'amenities_by_group' in amenity_data:
                        for category, amenities in amenity_data['amenities_by_group'].items():
                            for amenity_name in amenities:
                                normalized_name, normalized_category = self.amenity_normalizer.normalize_amenity_name(amenity_name)
                                all_amenities.add((normalized_name, normalized_category))

                    # Process top amenities
                    if 'top_amenities' in amenity_data:
                        for amenity_name in amenity_data['top_amenities'].keys():
                            normalized_name, normalized_category = self.amenity_normalizer.normalize_amenity_name(amenity_name)
                            all_amenities.add((normalized_name, normalized_category))

                except Exception as e:
                    self.stats['errors'].append(f"Error processing amenities from {amenity_file}: {e}")
                    logger.error(f"Failed to process amenities from {amenity_file}: {e}")

        self.stats['amenities_processed'] = len(all_amenities)
        logger.info(f"Extracted {len(all_amenities)} unique amenities")
        return all_amenities

    def load_amenities_to_database(self, amenities: Set[Tuple[str, str]]) -> Dict[str, int]:
        """
        Load amenities into database and return mapping of name to ID
        """
        logger.info("Loading amenities to database...")

        amenity_id_mapping = {}

        try:
            with self.db.get_cursor() as cursor:
                # Get existing amenities
                cursor.execute("SELECT id, name FROM amenity")
                existing_amenities = {row['name']: row['id'] for row in cursor.fetchall()}

                # Insert new amenities
                new_amenities = []
                for amenity_name, category in amenities:
                    if amenity_name not in existing_amenities:
                        new_amenities.append((amenity_name, category))
                    else:
                        amenity_id_mapping[amenity_name] = existing_amenities[amenity_name]

                if new_amenities:
                    # Batch insert new amenities
                    insert_query = """
                        INSERT INTO amenity (name, category, amenity_type, created_at, updated_at)
                        VALUES (%s, %s, 'accommodation', now(), now())
                        RETURNING id, name
                    """

                    for amenity_name, category in new_amenities:
                        cursor.execute(insert_query, (amenity_name, category))
                        result = cursor.fetchone()
                        amenity_id_mapping[result['name']] = result['id']
                        logger.debug(f"Inserted amenity: {amenity_name} (ID: {result['id']})")

                self.db.connection.commit()
                logger.info(f"Loaded {len(new_amenities)} new amenities, {len(existing_amenities)} already existed")

        except Exception as e:
            self.db.connection.rollback()
            self.stats['errors'].append(f"Amenity loading error: {e}")
            logger.error(f"Failed to load amenities: {e}")
            raise

        return amenity_id_mapping

    def run_phase2(self) -> Dict:
        """
        Execute complete Phase 2 reference data loading
        """
        logger.info("=== STARTING PHASE 2: REFERENCE DATA LOADING ===")

        try:
            # Step 1: Validate JSON files
            validation_results = self.validate_json_files()

            if validation_results['invalid']:
                logger.warning(f"Found {len(validation_results['invalid'])} invalid JSON files")
                for invalid_file in validation_results['invalid']:
                    logger.warning(f"Invalid file: {invalid_file}")

            # Step 2: Create city-filename mapping
            city_mapping = self.create_city_filename_mapping()

            # Step 3: Extract and normalize amenities
            all_amenities = self.extract_all_amenities()

            # Step 4: Load amenities to database
            amenity_mapping = self.load_amenities_to_database(all_amenities)

            # Compile results
            results = {
                'status': 'success',
                'validation_results': validation_results,
                'city_mapping': city_mapping,
                'amenity_mapping': amenity_mapping,
                'statistics': self.stats
            }

            logger.info("=== PHASE 2 COMPLETED SUCCESSFULLY ===")
            logger.info(f"Statistics: {self.stats}")

            return results

        except Exception as e:
            logger.error(f"Phase 2 failed: {e}")
            logger.error(traceback.format_exc())
            self.stats['errors'].append(f"Phase 2 failure: {e}")

            return {
                'status': 'failed',
                'error': str(e),
                'statistics': self.stats
            }

def main():
    """Main execution function"""

    # Database configuration - UPDATE THESE VALUES
    # DB_CONFIG = {
    #     'host': 'localhost',
    #     'database': 'your_database_name',
    #     'user': 'your_username',
    #     'password': 'your_password',
    #     'port': 5432
    # }

    # Data directory path - UPDATE THIS PATH
    DATA_DIRECTORY = "processed_data"

    # Initialize database connection
    db_connection = DatabaseConnection(**db_config)

    try:
        # Connect to database
        db_connection.connect()

        # Initialize Phase 2 loader
        loader = Phase2ReferenceDataLoader(db_connection, DATA_DIRECTORY)

        # Execute Phase 2
        results = loader.run_phase2()

        # Print results
        print("\n" + "="*60)
        print("PHASE 2 REFERENCE DATA LOADING RESULTS")
        print("="*60)
        print(f"Status: {results['status']}")
        print(f"Files validated: {results['statistics']['files_validated']}")
        print(f"Files invalid: {results['statistics']['files_invalid']}")
        print(f"Cities mapped: {results['statistics']['cities_mapped']}")
        print(f"Amenities processed: {results['statistics']['amenities_processed']}")

        if results['statistics']['errors']:
            print(f"\nErrors encountered: {len(results['statistics']['errors'])}")
            for error in results['statistics']['errors']:
                print(f"  - {error}")

        if results['status'] == 'success':
            print(f"\nCity mappings created: {len(results['city_mapping'])}")
            print(f"Amenity mappings created: {len(results['amenity_mapping'])}")
            print("\nPhase 2 completed successfully!")
        else:
            print(f"\nPhase 2 failed: {results.get('error', 'Unknown error')}")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Main execution failed: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

    finally:
        db_connection.close()

if __name__ == "__main__":
    main()
