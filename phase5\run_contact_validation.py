#!/usr/bin/env python3
"""
Phase 5 Contact Validation Script
Runs comprehensive validation queries to verify contact data integrity and coverage.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

def run_validation():
    """Run contact validation queries"""
    
    # Database configuration
    db_config = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    print("🔍 PHASE 5 CONTACT VALIDATION RESULTS")
    print("="*60)
    
    try:
        # Connect to database
        connection = psycopg2.connect(**db_config)
        connection.autocommit = False
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            
            # 1. Overall Contact Coverage Summary
            print("\n📊 OVERALL CONTACT COVERAGE")
            print("-" * 40)
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_accommodations,
                    COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END) as accommodations_with_phone,
                    COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END) as accommodations_with_website,
                    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as accommodations_with_address,
                    COUNT(CASE WHEN (phone IS NOT NULL AND phone != '') 
                                   OR (website IS NOT NULL AND website != '') 
                                   OR (l.address IS NOT NULL AND l.address != '') THEN 1 END) as accommodations_with_any_contact,
                    ROUND(
                        (COUNT(CASE WHEN phone IS NOT NULL AND phone != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as phone_coverage_pct,
                    ROUND(
                        (COUNT(CASE WHEN website IS NOT NULL AND website != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as website_coverage_pct,
                    ROUND(
                        (COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as address_coverage_pct,
                    ROUND(
                        (COUNT(CASE WHEN (phone IS NOT NULL AND phone != '') 
                                         OR (website IS NOT NULL AND website != '') 
                                         OR (l.address IS NOT NULL AND l.address != '') THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as overall_contact_coverage_pct
                FROM accommodation acc
                JOIN location l ON acc.location_id = l.id
                JOIN city c ON l.city_id = c.id
                WHERE c.name IN ('Casablanca', 'Marrakech')
            """)
            
            result = cursor.fetchone()
            print(f"Total accommodations: {result['total_accommodations']}")
            print(f"With phone: {result['accommodations_with_phone']} ({result['phone_coverage_pct']}%)")
            print(f"With website: {result['accommodations_with_website']} ({result['website_coverage_pct']}%)")
            print(f"With address: {result['accommodations_with_address']} ({result['address_coverage_pct']}%)")
            print(f"With any contact: {result['accommodations_with_any_contact']} ({result['overall_contact_coverage_pct']}%)")
            
            # 2. Contact Coverage by City
            print("\n🏙️ CONTACT COVERAGE BY CITY")
            print("-" * 40)
            cursor.execute("""
                SELECT 
                    c.name as city_name,
                    COUNT(*) as total_accommodations,
                    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as with_phone,
                    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as with_website,
                    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address,
                    ROUND(
                        (COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as phone_coverage_pct,
                    ROUND(
                        (COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as website_coverage_pct,
                    ROUND(
                        (COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as address_coverage_pct
                FROM city c
                JOIN location l ON c.id = l.city_id
                JOIN accommodation acc ON l.id = acc.location_id
                WHERE c.name IN ('Casablanca', 'Marrakech')
                GROUP BY c.id, c.name
                ORDER BY c.name
            """)
            
            for row in cursor.fetchall():
                print(f"{row['city_name']}:")
                print(f"  Total accommodations: {row['total_accommodations']}")
                print(f"  Phone coverage: {row['with_phone']} ({row['phone_coverage_pct']}%)")
                print(f"  Website coverage: {row['with_website']} ({row['website_coverage_pct']}%)")
                print(f"  Address coverage: {row['with_address']} ({row['address_coverage_pct']}%)")
            
            # 3. Contact Data Quality Analysis
            print("\n🔍 CONTACT DATA QUALITY")
            print("-" * 40)
            cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as total_phones,
                    COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' AND acc.phone ~ '^\\+?[0-9\\(\\)\\-\\s]+$' THEN 1 END) as valid_phone_format,
                    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as total_websites,
                    COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' AND acc.website ~ '^https?://' THEN 1 END) as valid_website_format,
                    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as total_addresses,
                    COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' AND LENGTH(l.address) > 10 THEN 1 END) as substantial_addresses
                FROM accommodation acc
                JOIN location l ON acc.location_id = l.id
                JOIN city c ON l.city_id = c.id
                WHERE c.name IN ('Casablanca', 'Marrakech')
            """)
            
            result = cursor.fetchone()
            print(f"Phone numbers: {result['total_phones']} total, {result['valid_phone_format']} valid format")
            print(f"Websites: {result['total_websites']} total, {result['valid_website_format']} valid format")
            print(f"Addresses: {result['total_addresses']} total, {result['substantial_addresses']} substantial")
            
            # 4. Sample Complete Contact Information
            print("\n📋 SAMPLE COMPLETE CONTACT INFO")
            print("-" * 40)
            cursor.execute("""
                SELECT 
                    acc.name as accommodation_name,
                    c.name as city_name,
                    acc.phone,
                    CASE 
                        WHEN LENGTH(acc.website) > 40 
                        THEN LEFT(acc.website, 37) || '...'
                        ELSE acc.website 
                    END as website_preview,
                    l.address
                FROM accommodation acc
                JOIN location l ON acc.location_id = l.id
                JOIN city c ON l.city_id = c.id
                WHERE c.name IN ('Casablanca', 'Marrakech')
                    AND acc.phone IS NOT NULL AND acc.phone != ''
                    AND acc.website IS NOT NULL AND acc.website != ''
                    AND l.address IS NOT NULL AND l.address != ''
                ORDER BY c.name, acc.name
                LIMIT 5
            """)
            
            for row in cursor.fetchall():
                print(f"{row['accommodation_name']} ({row['city_name']}):")
                print(f"  Phone: {row['phone']}")
                print(f"  Website: {row['website_preview']}")
                print(f"  Address: {row['address']}")
            
            # 5. External ID Coverage
            print("\n🔗 EXTERNAL ID COVERAGE")
            print("-" * 40)
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_accommodations,
                    COUNT(CASE WHEN external_id IS NOT NULL AND external_id != '' THEN 1 END) as with_external_id,
                    ROUND(
                        (COUNT(CASE WHEN external_id IS NOT NULL AND external_id != '' THEN 1 END)::decimal / COUNT(*)) * 100, 
                        1
                    ) as external_id_coverage_pct
                FROM accommodation acc
                JOIN location l ON acc.location_id = l.id
                JOIN city c ON l.city_id = c.id
                WHERE c.name IN ('Casablanca', 'Marrakech')
            """)
            
            result = cursor.fetchone()
            print(f"Total accommodations: {result['total_accommodations']}")
            print(f"With external ID: {result['with_external_id']} ({result['external_id_coverage_pct']}%)")
            
            # 6. Final Data Quality Score
            print("\n🎯 FINAL DATA QUALITY SCORE")
            print("-" * 40)
            cursor.execute("""
                WITH quality_metrics AS (
                    SELECT 
                        COUNT(*) as total_accommodations,
                        COUNT(CASE WHEN acc.phone IS NOT NULL AND acc.phone != '' THEN 1 END) as with_phone,
                        COUNT(CASE WHEN acc.website IS NOT NULL AND acc.website != '' THEN 1 END) as with_website,
                        COUNT(CASE WHEN l.address IS NOT NULL AND l.address != '' THEN 1 END) as with_address,
                        COUNT(CASE WHEN (acc.phone IS NOT NULL AND acc.phone != '') 
                                     OR (acc.website IS NOT NULL AND acc.website != '') 
                                     OR (l.address IS NOT NULL AND l.address != '') THEN 1 END) as with_any_contact
                    FROM accommodation acc
                    JOIN location l ON acc.location_id = l.id
                    JOIN city c ON l.city_id = c.id
                    WHERE c.name IN ('Casablanca', 'Marrakech')
                )
                SELECT 
                    total_accommodations,
                    with_phone,
                    with_website,
                    with_address,
                    with_any_contact,
                    ROUND((with_phone::decimal / total_accommodations) * 100, 1) as phone_coverage_pct,
                    ROUND((with_website::decimal / total_accommodations) * 100, 1) as website_coverage_pct,
                    ROUND((with_address::decimal / total_accommodations) * 100, 1) as address_coverage_pct,
                    ROUND((with_any_contact::decimal / total_accommodations) * 100, 1) as overall_contact_coverage_pct,
                    ROUND(
                        ((with_phone + with_website + with_address)::decimal / (total_accommodations * 3)) * 100, 
                        1
                    ) as comprehensive_contact_quality_score
                FROM quality_metrics
            """)
            
            result = cursor.fetchone()
            print(f"Total accommodations: {result['total_accommodations']}")
            print(f"Phone coverage: {result['phone_coverage_pct']}%")
            print(f"Website coverage: {result['website_coverage_pct']}%")
            print(f"Address coverage: {result['address_coverage_pct']}%")
            print(f"Overall contact coverage: {result['overall_contact_coverage_pct']}%")
            print(f"Comprehensive quality score: {result['comprehensive_contact_quality_score']}%")
            
        connection.close()
        print("\n✅ Validation completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        return False

def main():
    """Main validation function"""
    success = run_validation()
    
    if success:
        print("\n🎉 Phase 5 contact data validation passed!")
        print("📞 Contact information is properly loaded and accessible.")
    else:
        print("\n⚠️ Phase 5 contact data validation issues found.")
        print("🔧 Check the database and contact data integrity.")
    
    print("="*60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
