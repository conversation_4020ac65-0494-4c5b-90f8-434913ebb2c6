#!/usr/bin/env python3
"""
Quick test to verify Phase 3 fixes work with actual JSON structure
Tests the hotel extraction logic without database connection
"""

import json
import sys
from pathlib import Path

def test_hotel_extraction(data_directory: str, city_filename: str):
    """Test hotel extraction from actual JSON structure"""
    
    data_dir = Path(data_directory)
    city_file = data_dir / city_filename
    
    print(f"\n{'='*60}")
    print(f"TESTING HOTEL EXTRACTION: {city_filename}")
    print(f"{'='*60}")
    
    if not city_file.exists():
        print(f"❌ File not found: {city_file}")
        return False
    
    try:
        with open(city_file, 'r', encoding='utf-8') as f:
            city_data = json.load(f)
        
        print(f"✅ File loaded successfully")
        
        # Test the actual extraction logic from Phase 3
        hotels_data = []
        for hotel_id_key, hotel_entry in city_data.items():
            if isinstance(hotel_entry, dict) and 'hotel_details' in hotel_entry:
                # Add the hotel_id to the hotel_details for reference
                hotel_details = hotel_entry['hotel_details'].copy()
                hotel_details['external_hotel_id'] = hotel_entry.get('hotel_id', hotel_id_key)
                hotel_details['distance_label'] = hotel_entry.get('distance_label')
                hotels_data.append(hotel_details)
        
        print(f"🏨 Hotels extracted: {len(hotels_data)}")
        
        if len(hotels_data) > 0:
            # Test field extraction for first few hotels
            print(f"\n🔍 TESTING FIELD EXTRACTION (first 3 hotels):")
            
            for i, hotel_data in enumerate(hotels_data[:3]):
                print(f"\n  Hotel {i+1}:")
                
                # Test name extraction
                hotel_name = hotel_data.get('name', '').strip()
                print(f"    Name: {hotel_name}")
                
                # Test coordinates extraction
                coordinates = hotel_data.get('coordinates', {})
                latitude = coordinates.get('latitude')
                longitude = coordinates.get('longitude')
                print(f"    Coordinates: {latitude}, {longitude}")
                
                # Test stars extraction
                stars = hotel_data.get('hotel_stars')
                print(f"    Stars: {stars}")
                
                # Test rating extraction
                rating_info = hotel_data.get('rating', {})
                rating_value = rating_info.get('rating') if isinstance(rating_info, dict) else None
                print(f"    Rating: {rating_value}")
                
                # Test construction year
                construction_year = hotel_data.get('construction_year')
                print(f"    Construction year: {construction_year}")
                
                # Test highlights
                highlights = hotel_data.get('highlights', [])
                print(f"    Highlights: {len(highlights)} items")
                if highlights:
                    print(f"      First highlight: {highlights[0]}")
                
                # Test external ID
                external_id = hotel_data.get('external_hotel_id')
                print(f"    External ID: {external_id}")
                
                # Test distance label
                distance = hotel_data.get('distance_label')
                print(f"    Distance: {distance}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    
    # Data directory path - UPDATE THIS PATH
    DATA_DIRECTORY = "processed_data"
    
    if not Path(DATA_DIRECTORY).exists():
        print(f"❌ Data directory '{DATA_DIRECTORY}' does not exist!")
        print("Please update the DATA_DIRECTORY variable in this script.")
        sys.exit(1)
    
    print("🔧 PHASE 3 FIXES VERIFICATION TEST")
    print("Testing hotel extraction with actual JSON structure")
    
    # Test both target cities
    cities_to_test = [
        'casablanca.json',
        'marrakech.json'
    ]
    
    results = {}
    for city_file in cities_to_test:
        results[city_file] = test_hotel_extraction(DATA_DIRECTORY, city_file)
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    for city_file, success in results.items():
        status = "✅ Ready" if success else "❌ Issues"
        print(f"{city_file}: {status}")
    
    all_ready = all(results.values())
    if all_ready:
        print(f"\n🎉 Phase 3 fixes are working correctly!")
        print(f"Hotels are being extracted from the actual JSON structure.")
        print(f"You can now run: python phase3_core_data_loader.py")
    else:
        print(f"\n⚠️ Some issues found. Please check the output above.")
    
    return all_ready

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
