#!/usr/bin/env python3
"""
Phase 4 Data Structure Test
Quick test to verify the amenity and review data structure before running the full loader.
"""

import os
import json
import sys
from typing import Dict, List

def test_amenity_structure():
    """Test amenity data structure"""
    print("🔍 Testing Amenity Data Structure...")
    
    # Test Casablanca amenity file
    amenity_file = "processed_data/amenities/casablanca/897965.json"
    
    if not os.path.exists(amenity_file):
        print(f"❌ Amenity file not found: {amenity_file}")
        return False
    
    try:
        with open(amenity_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Successfully loaded amenity file: {amenity_file}")
        
        # Check structure
        hotel_details = data.get('hotel_details', [])
        if not hotel_details:
            print("❌ No hotel_details found in amenity data")
            return False
        
        hotel_info = hotel_details[0]
        amenities_by_group = hotel_info.get('amenities_by_group', {})
        top_amenities = hotel_info.get('top_amenities', {})
        
        print(f"✅ Found {len(amenities_by_group)} amenity groups")
        print(f"✅ Found {len(top_amenities)} top amenities")
        
        # Show sample data
        print("\n📋 Sample Amenity Groups:")
        for group_name, amenities in list(amenities_by_group.items())[:3]:
            print(f"  {group_name}: {len(amenities)} amenities")
            print(f"    Sample: {amenities[:3]}")
        
        print("\n📋 Sample Top Amenities:")
        for amenity_name, details in list(top_amenities.items())[:3]:
            print(f"  {amenity_name}: {details}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading amenity data: {e}")
        return False

def test_review_structure():
    """Test review data structure"""
    print("\n🔍 Testing Review Data Structure...")
    
    # Test Casablanca review file
    review_file = "processed_data/reviews/casablanca/897965.json"
    
    if not os.path.exists(review_file):
        print(f"❌ Review file not found: {review_file}")
        return False
    
    try:
        with open(review_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Successfully loaded review file: {review_file}")
        
        # Check structure
        overall_rating = data.get('rating')
        rating_count = data.get('ratingCount')
        aspect_ratings = data.get('aspectRatings', [])
        advertiser_ratings = data.get('advertiserRatings', [])
        review_ratings = data.get('reviewRatings', {})
        individual_reviews = review_ratings.get('reviews', [])
        
        print(f"✅ Overall rating: {overall_rating} ({rating_count} reviews)")
        print(f"✅ Found {len(aspect_ratings)} aspect ratings")
        print(f"✅ Found {len(advertiser_ratings)} advertiser ratings")
        print(f"✅ Found {len(individual_reviews)} individual reviews")
        
        # Show sample data
        print("\n📋 Sample Aspect Ratings:")
        for aspect in aspect_ratings[:3]:
            print(f"  {aspect.get('type_of_aspect')}: {aspect.get('value')}")
        
        print("\n📋 Sample Advertiser Ratings:")
        for advertiser in advertiser_ratings:
            print(f"  {advertiser.get('advertiser')}: {advertiser.get('value')}")
        
        print("\n📋 Sample Individual Reviews:")
        for review in individual_reviews[:2]:
            print(f"  Rating: {review.get('rating')}, Author: {review.get('author')}")
            print(f"  Text: {review.get('text', '')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading review data: {e}")
        return False

def test_hotel_data_structure():
    """Test main hotel data structure"""
    print("\n🔍 Testing Hotel Data Structure...")
    
    # Test Casablanca hotel file
    hotel_file = "processed_data/casablanca.json"
    
    if not os.path.exists(hotel_file):
        print(f"❌ Hotel file not found: {hotel_file}")
        return False
    
    try:
        with open(hotel_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Successfully loaded hotel file: {hotel_file}")
        print(f"✅ Found {len(data)} hotels")
        
        # Check sample hotel structure
        sample_external_id = list(data.keys())[0]
        sample_hotel = data[sample_external_id]
        
        hotel_id = sample_hotel.get('hotel_id')
        hotel_details = sample_hotel.get('hotel_details', {})
        hotel_name = hotel_details.get('name')
        
        print(f"✅ Sample hotel: {hotel_name} (External ID: {sample_external_id}, Hotel ID: {hotel_id})")
        
        # Check if corresponding amenity and review files exist
        amenity_file = f"processed_data/amenities/casablanca/{hotel_id}.json"
        review_file = f"processed_data/reviews/casablanca/{hotel_id}.json"
        
        amenity_exists = os.path.exists(amenity_file)
        review_exists = os.path.exists(review_file)
        
        print(f"  Amenity file exists: {'✅' if amenity_exists else '❌'}")
        print(f"  Review file exists: {'✅' if review_exists else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading hotel data: {e}")
        return False

def test_data_coverage():
    """Test data coverage across cities"""
    print("\n🔍 Testing Data Coverage...")
    
    cities = ['casablanca', 'marrakech']
    
    for city in cities:
        print(f"\n📍 Testing {city.title()}:")
        
        # Check main hotel file
        hotel_file = f"processed_data/{city}.json"
        if not os.path.exists(hotel_file):
            print(f"  ❌ Hotel file missing: {hotel_file}")
            continue
        
        with open(hotel_file, 'r', encoding='utf-8') as f:
            hotels = json.load(f)
        
        print(f"  ✅ {len(hotels)} hotels found")
        
        # Check amenity and review directories
        amenity_dir = f"processed_data/amenities/{city}"
        review_dir = f"processed_data/reviews/{city}"
        
        amenity_files = len(os.listdir(amenity_dir)) if os.path.exists(amenity_dir) else 0
        review_files = len(os.listdir(review_dir)) if os.path.exists(review_dir) else 0
        
        print(f"  ✅ {amenity_files} amenity files")
        print(f"  ✅ {review_files} review files")
        
        # Calculate coverage
        hotel_count = len(hotels)
        amenity_coverage = (amenity_files / hotel_count) * 100 if hotel_count > 0 else 0
        review_coverage = (review_files / hotel_count) * 100 if hotel_count > 0 else 0
        
        print(f"  📊 Amenity coverage: {amenity_coverage:.1f}%")
        print(f"  📊 Review coverage: {review_coverage:.1f}%")

def main():
    """Main test function"""
    print("="*60)
    print("PHASE 4 DATA STRUCTURE TEST")
    print("="*60)
    
    # Change to the parent directory to access processed_data
    if os.path.basename(os.getcwd()) == 'phase4':
        os.chdir('..')
    
    tests = [
        test_hotel_data_structure,
        test_amenity_structure,
        test_review_structure,
        test_data_coverage
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
    
    print("\n" + "="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to run Phase 4 loader.")
    else:
        print("⚠️  Some tests failed. Check data structure before running loader.")
    
    print("="*60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
