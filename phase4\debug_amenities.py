#!/usr/bin/env python3
"""
Debug script to investigate amenity mapping issues
"""

import psycopg2
from psycopg2.extras import RealDictCursor

def debug_amenities():
    """Debug amenity mappings"""
    
    # Database configuration
    db_config = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    print("🔍 DEBUGGING AMENITY MAPPINGS")
    print("="*50)
    
    try:
        connection = psycopg2.connect(**db_config)
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            
            # Check amenity table
            print("\n1. AMENITY TABLE")
            cursor.execute("SELECT COUNT(*) as count FROM amenity")
            result = cursor.fetchone()
            print(f"Total amenities: {result['count']}")
            
            # Check accommodation_amenity_junction table
            print("\n2. ACCOMMODATION_AMENITY_JUNCTION TABLE")
            cursor.execute("SELECT COUNT(*) as count FROM accommodation_amenity_junction")
            result = cursor.fetchone()
            print(f"Total junction records: {result['count']}")
            
            # Check if junction table has data
            if result['count'] > 0:
                cursor.execute("""
                    SELECT 
                        accommodation_id, 
                        amenity_id, 
                        is_free, 
                        is_available, 
                        is_top_amenity 
                    FROM accommodation_amenity_junction 
                    LIMIT 5
                """)
                
                print("\nSample junction records:")
                for row in cursor.fetchall():
                    print(f"  Accommodation {row['accommodation_id']} -> Amenity {row['amenity_id']} (free: {row['is_free']}, available: {row['is_available']}, top: {row['is_top_amenity']})")
            
            # Check accommodation table
            print("\n3. ACCOMMODATION TABLE")
            cursor.execute("SELECT COUNT(*) as count FROM accommodation")
            result = cursor.fetchone()
            print(f"Total accommodations: {result['count']}")
            
            # Check for accommodations with external_id
            cursor.execute("SELECT COUNT(*) as count FROM accommodation WHERE external_id IS NOT NULL")
            result = cursor.fetchone()
            print(f"Accommodations with external_id: {result['count']}")
            
            # Check join between tables
            print("\n4. JOIN ANALYSIS")
            cursor.execute("""
                SELECT 
                    COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
                    COUNT(DISTINCT aaj.amenity_id) as amenities_used,
                    COUNT(*) as total_mappings
                FROM accommodation_amenity_junction aaj
                JOIN accommodation acc ON aaj.accommodation_id = acc.id
                JOIN amenity a ON aaj.amenity_id = a.id
            """)
            
            result = cursor.fetchone()
            print(f"Accommodations with amenities (via join): {result['accommodations_with_amenities']}")
            print(f"Amenities used: {result['amenities_used']}")
            print(f"Total valid mappings: {result['total_mappings']}")
            
            # Check for orphaned records
            print("\n5. ORPHANED RECORDS CHECK")
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM accommodation_amenity_junction aaj
                LEFT JOIN accommodation acc ON aaj.accommodation_id = acc.id
                WHERE acc.id IS NULL
            """)
            result = cursor.fetchone()
            print(f"Orphaned accommodation mappings: {result['count']}")
            
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM accommodation_amenity_junction aaj
                LEFT JOIN amenity a ON aaj.amenity_id = a.id
                WHERE a.id IS NULL
            """)
            result = cursor.fetchone()
            print(f"Orphaned amenity mappings: {result['count']}")
            
            # Sample amenities
            print("\n6. SAMPLE AMENITIES")
            cursor.execute("SELECT id, name, category FROM amenity ORDER BY id LIMIT 10")
            for row in cursor.fetchall():
                print(f"  {row['id']}: {row['name']} ({row['category']})")
            
            # Sample accommodations
            print("\n7. SAMPLE ACCOMMODATIONS")
            cursor.execute("SELECT id, name, external_id FROM accommodation ORDER BY id LIMIT 5")
            for row in cursor.fetchall():
                print(f"  {row['id']}: {row['name']} (ext_id: {row['external_id']})")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    debug_amenities()
