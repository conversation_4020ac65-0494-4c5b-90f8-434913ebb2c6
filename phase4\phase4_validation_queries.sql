-- Phase 4 Validation Queries: Amenity and Review Relationships
-- These queries validate the successful loading of amenities and reviews for accommodations

-- =============================================================================
-- AMENITY VALIDATION QUERIES
-- =============================================================================

-- 1. Overall Amenity Statistics
SELECT
    'Amenity Overview' as metric,
    COUNT(DISTINCT a.id) as total_amenities,
    COUNT(DISTINCT a.category) as unique_categories,
    COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
    COUNT(*) as total_amenity_mappings
FROM amenity a
LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id;

-- 2. Amenity Categories Breakdown
SELECT
    a.category,
    COUNT(DISTINCT a.id) as unique_amenities,
    COUNT(aaj.id) as total_mappings,
    COUNT(DISTINCT aaj.accommodation_id) as accommodations_count
FROM amenity a
LEFT JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id
GROUP BY a.category
ORDER BY total_mappings DESC;

-- 3. Top Amenities Analysis
SELECT
    a.name,
    a.category,
    COUNT(aaj.id) as mapping_count,
    COUNT(CASE WHEN aaj.is_top_amenity = true THEN 1 END) as top_amenity_count,
    COUNT(CASE WHEN aaj.is_free = true THEN 1 END) as free_count
FROM amenity a
JOIN accommodation_amenity_junction aaj ON a.id = aaj.amenity_id
GROUP BY a.id, a.name, a.category
ORDER BY mapping_count DESC
LIMIT 20;

-- 4. Amenity Coverage by City
SELECT
    c.name as city_name,
    COUNT(DISTINCT acc.id) as total_accommodations,
    COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
    ROUND(
        (COUNT(DISTINCT aaj.accommodation_id)::decimal / COUNT(DISTINCT acc.id)) * 100,
        2
    ) as amenity_coverage_percentage,
    COUNT(aaj.id) as total_amenity_mappings
FROM city c
JOIN location l ON c.id = l.city_id
JOIN accommodation acc ON l.id = acc.location_id
LEFT JOIN accommodation_amenity_junction aaj ON acc.id = aaj.accommodation_id
GROUP BY c.id, c.name
ORDER BY amenity_coverage_percentage DESC;

-- 5. Sample Accommodations with Amenities
SELECT
    acc.name as accommodation_name,
    acc.external_id,
    c.name as city_name,
    COUNT(aaj.id) as amenity_count,
    STRING_AGG(DISTINCT a.category, ', ') as amenity_categories
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN accommodation_amenity_junction aaj ON acc.id = aaj.accommodation_id
LEFT JOIN amenity a ON aaj.amenity_id = a.id
GROUP BY acc.id, acc.name, acc.external_id, c.name
HAVING COUNT(aaj.id) > 0
ORDER BY amenity_count DESC
LIMIT 10;

-- =============================================================================
-- REVIEW VALIDATION QUERIES
-- =============================================================================

-- 6. Overall Review Statistics (Updated for V7 Schema)
SELECT
    'Review Overview' as metric,
    COUNT(DISTINCT r.id) as total_main_reviews,
    COUNT(DISTINCT r.entity_id) as accommodations_with_reviews,
    COUNT(DISTINCT re.id) as total_review_entries,
    COUNT(DISTINCT rar.id) as total_aspect_ratings,
    AVG(r.overall_rating) as avg_overall_rating,
    SUM(r.review_count) as total_review_count
FROM review r
LEFT JOIN review_entry re ON r.id = re.review_id
LEFT JOIN review_aspect_rating rar ON r.id = rar.review_id
WHERE r.entity_type = 'accommodation';

-- 7. Review Structure Breakdown (Updated for V7 Schema)
SELECT
    'Main Reviews' as review_type,
    COUNT(*) as review_count,
    COUNT(DISTINCT entity_id) as unique_accommodations,
    AVG(overall_rating) as avg_rating,
    MIN(overall_rating) as min_rating,
    MAX(overall_rating) as max_rating
FROM review
WHERE entity_type = 'accommodation' AND overall_rating IS NOT NULL

UNION ALL

SELECT
    'Individual Review Entries' as review_type,
    COUNT(*) as review_count,
    COUNT(DISTINCT r.entity_id) as unique_accommodations,
    AVG(re.rating) as avg_rating,
    MIN(re.rating) as min_rating,
    MAX(re.rating) as max_rating
FROM review_entry re
JOIN review r ON re.review_id = r.id
WHERE r.entity_type = 'accommodation' AND re.rating IS NOT NULL

UNION ALL

SELECT
    'Aspect Ratings' as review_type,
    COUNT(*) as review_count,
    COUNT(DISTINCT r.entity_id) as unique_accommodations,
    AVG(rar.rating_value) as avg_rating,
    MIN(rar.rating_value) as min_rating,
    MAX(rar.rating_value) as max_rating
FROM review_aspect_rating rar
JOIN review r ON rar.review_id = r.id
WHERE r.entity_type = 'accommodation'
ORDER BY review_count DESC;

-- 8. Review Coverage by City (Updated for V7 Schema)
SELECT
    c.name as city_name,
    COUNT(DISTINCT acc.id) as total_accommodations,
    COUNT(DISTINCT r.entity_id) as accommodations_with_reviews,
    ROUND(
        (COUNT(DISTINCT r.entity_id)::decimal / COUNT(DISTINCT acc.id)) * 100,
        2
    ) as review_coverage_percentage,
    COUNT(r.id) as total_main_reviews,
    COUNT(re.id) as total_review_entries,
    COUNT(rar.id) as total_aspect_ratings,
    AVG(r.overall_rating) as avg_rating
FROM city c
JOIN location l ON c.id = l.city_id
JOIN accommodation acc ON l.id = acc.location_id
LEFT JOIN review r ON acc.id = r.entity_id AND r.entity_type = 'accommodation'
LEFT JOIN review_entry re ON r.id = re.review_id
LEFT JOIN review_aspect_rating rar ON r.id = rar.review_id
GROUP BY c.id, c.name
ORDER BY review_coverage_percentage DESC;

-- 9. Review Sources Analysis (Updated for V7 Schema)
SELECT
    'Review Entries by Advertiser' as analysis_type,
    re.advertiser as source,
    COUNT(*) as review_count,
    COUNT(DISTINCT r.entity_id) as unique_accommodations,
    AVG(re.rating) as avg_rating
FROM review_entry re
JOIN review r ON re.review_id = r.id
WHERE r.entity_type = 'accommodation' AND re.advertiser IS NOT NULL
GROUP BY re.advertiser
ORDER BY review_count DESC;

-- 10. Sample Accommodations with Reviews (Updated for V7 Schema)
SELECT
    acc.name as accommodation_name,
    acc.external_id,
    c.name as city_name,
    COUNT(DISTINCT r.id) as main_reviews,
    COUNT(DISTINCT re.id) as review_entries,
    COUNT(DISTINCT rar.id) as aspect_ratings,
    AVG(r.overall_rating) as avg_overall_rating,
    MAX(r.review_count) as total_review_count
FROM accommodation acc
JOIN location l ON acc.location_id = l.id
JOIN city c ON l.city_id = c.id
LEFT JOIN review r ON acc.id = r.entity_id AND r.entity_type = 'accommodation'
LEFT JOIN review_entry re ON r.id = re.review_id
LEFT JOIN review_aspect_rating rar ON r.id = rar.review_id
GROUP BY acc.id, acc.name, acc.external_id, c.name
HAVING COUNT(DISTINCT r.id) > 0
ORDER BY review_entries DESC
LIMIT 10;

-- =============================================================================
-- ASPECT RATINGS ANALYSIS (Updated for V7 Schema)
-- =============================================================================

-- 11. Review Aspects Breakdown
SELECT
    rar.aspect_type as aspect_name,
    COUNT(*) as aspect_count,
    AVG(rar.rating_value) as avg_rating,
    MIN(rar.rating_value) as min_rating,
    MAX(rar.rating_value) as max_rating,
    COUNT(DISTINCT r.entity_id) as accommodations_count
FROM review_aspect_rating rar
JOIN review r ON rar.review_id = r.id
WHERE r.entity_type = 'accommodation'
GROUP BY rar.aspect_type
ORDER BY aspect_count DESC;

-- 12. Individual Reviews Analysis (Updated for V7 Schema)
SELECT
    'Individual Review Entries' as metric,
    COUNT(*) as total_individual_reviews,
    COUNT(CASE WHEN re.author IS NOT NULL THEN 1 END) as reviews_with_author,
    COUNT(CASE WHEN re.review_text IS NOT NULL THEN 1 END) as reviews_with_content,
    COUNT(CASE WHEN re.travelled_at IS NOT NULL THEN 1 END) as reviews_with_travel_date,
    COUNT(CASE WHEN re.created_at IS NOT NULL THEN 1 END) as reviews_with_creation_date,
    COUNT(CASE WHEN re.advertiser IS NOT NULL THEN 1 END) as reviews_with_advertiser,
    AVG(LENGTH(re.review_text)) as avg_content_length
FROM review_entry re
JOIN review r ON re.review_id = r.id
WHERE r.entity_type = 'accommodation';

-- =============================================================================
-- DATA INTEGRITY CHECKS
-- =============================================================================

-- 13. External ID Consistency Check
SELECT
    'External ID Consistency' as check_name,
    COUNT(DISTINCT acc.external_id) as unique_external_ids,
    COUNT(acc.id) as total_accommodations,
    CASE
        WHEN COUNT(DISTINCT acc.external_id) = COUNT(acc.id)
        THEN 'PASS: All external IDs are unique'
        ELSE 'FAIL: Duplicate external IDs found'
    END as result
FROM accommodation acc;

-- 14. Amenity Mapping Integrity
SELECT
    'Amenity Mapping Integrity' as check_name,
    COUNT(*) as total_mappings,
    COUNT(CASE WHEN a.id IS NULL THEN 1 END) as orphaned_amenity_mappings,
    COUNT(CASE WHEN acc.id IS NULL THEN 1 END) as orphaned_accommodation_mappings,
    CASE
        WHEN COUNT(CASE WHEN a.id IS NULL OR acc.id IS NULL THEN 1 END) = 0
        THEN 'PASS: All mappings have valid references'
        ELSE 'FAIL: Orphaned mappings found'
    END as result
FROM accommodation_amenity_junction aaj
LEFT JOIN amenity a ON aaj.amenity_id = a.id
LEFT JOIN accommodation acc ON aaj.accommodation_id = acc.id;

-- 15. Review Entity Integrity (Updated for V7 Schema)
SELECT
    'Review Entity Integrity' as check_name,
    COUNT(*) as total_main_reviews,
    COUNT(CASE WHEN acc.id IS NULL THEN 1 END) as orphaned_main_reviews,
    (SELECT COUNT(*) FROM review_entry re
     LEFT JOIN review r ON re.review_id = r.id
     WHERE r.id IS NULL) as orphaned_review_entries,
    (SELECT COUNT(*) FROM review_aspect_rating rar
     LEFT JOIN review r ON rar.review_id = r.id
     WHERE r.id IS NULL) as orphaned_aspect_ratings,
    CASE
        WHEN COUNT(CASE WHEN acc.id IS NULL THEN 1 END) = 0
        THEN 'PASS: All main reviews linked to valid accommodations'
        ELSE 'FAIL: Orphaned main reviews found'
    END as result
FROM review r
LEFT JOIN accommodation acc ON r.entity_id = acc.id
WHERE r.entity_type = 'accommodation';

-- =============================================================================
-- SUMMARY STATISTICS (Updated for V7 Schema)
-- =============================================================================

-- 16. Phase 4 Loading Summary
SELECT
    'Phase 4 Summary' as summary,
    (SELECT COUNT(*) FROM amenity) as total_amenities,
    (SELECT COUNT(*) FROM accommodation_amenity_junction) as total_amenity_mappings,
    (SELECT COUNT(*) FROM review WHERE entity_type = 'accommodation') as total_main_reviews,
    (SELECT COUNT(*) FROM review_entry re JOIN review r ON re.review_id = r.id WHERE r.entity_type = 'accommodation') as total_review_entries,
    (SELECT COUNT(*) FROM review_aspect_rating rar JOIN review r ON rar.review_id = r.id WHERE r.entity_type = 'accommodation') as total_aspect_ratings,
    (SELECT COUNT(DISTINCT entity_id) FROM review WHERE entity_type = 'accommodation') as accommodations_with_reviews,
    (SELECT COUNT(DISTINCT aaj.accommodation_id) FROM accommodation_amenity_junction aaj) as accommodations_with_amenities;

-- 17. Data Quality Score (Updated for V7 Schema)
WITH quality_metrics AS (
    SELECT
        COUNT(DISTINCT acc.id) as total_accommodations,
        COUNT(DISTINCT aaj.accommodation_id) as accommodations_with_amenities,
        COUNT(DISTINCT r.entity_id) as accommodations_with_reviews,
        COUNT(DISTINCT re.review_id) as accommodations_with_review_entries,
        COUNT(DISTINCT rar.review_id) as accommodations_with_aspect_ratings
    FROM accommodation acc
    LEFT JOIN accommodation_amenity_junction aaj ON acc.id = aaj.accommodation_id
    LEFT JOIN review r ON acc.id = r.entity_id AND r.entity_type = 'accommodation'
    LEFT JOIN review_entry re ON r.id = re.review_id
    LEFT JOIN review_aspect_rating rar ON r.id = rar.review_id
)
SELECT
    'Data Quality Score' as metric,
    total_accommodations,
    accommodations_with_amenities,
    accommodations_with_reviews,
    accommodations_with_review_entries,
    accommodations_with_aspect_ratings,
    ROUND((accommodations_with_amenities::decimal / total_accommodations) * 100, 2) as amenity_coverage_pct,
    ROUND((accommodations_with_reviews::decimal / total_accommodations) * 100, 2) as review_coverage_pct,
    ROUND((accommodations_with_review_entries::decimal / total_accommodations) * 100, 2) as review_entries_coverage_pct,
    ROUND((accommodations_with_aspect_ratings::decimal / total_accommodations) * 100, 2) as aspect_ratings_coverage_pct,
    ROUND(
        ((accommodations_with_amenities + accommodations_with_reviews)::decimal / (total_accommodations * 2)) * 100,
        2
    ) as overall_data_quality_score
FROM quality_metrics;
