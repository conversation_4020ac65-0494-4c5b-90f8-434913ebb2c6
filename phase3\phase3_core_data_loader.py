#!/usr/bin/env python3
"""
Phase 3: Core Hotel Data Loading - City by City Approach
Loads core hotel data (accommodations, locations) for Casablanca and Marrakech only
Simple, robust, and easy to debug implementation
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql
import traceback
from decimal import Decimal


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase3_loading.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnection:
    """Manages PostgreSQL database connections with proper error handling"""

    def __init__(self, host: str, database: str, user: str, password: str, port: int = 5432):
        self.connection_params = {
            'host': host,
            'database': database,
            'user': user,
            'password': password,
            'port': port
        }
        self.connection = None

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.connection_params)
            self.connection.autocommit = False
            logger.info("Database connection established successfully")
            return self.connection
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")

    def get_cursor(self):
        """Get database cursor with RealDictCursor for named access"""
        if not self.connection:
            self.connect()
        return self.connection.cursor(cursor_factory=RealDictCursor)

class SimpleCityProcessor:
    """Simple city-by-city data processor - easy to understand and debug"""

    def __init__(self, db_connection: DatabaseConnection, data_directory: str):
        self.db = db_connection
        self.data_dir = Path(data_directory)

        # Simple city configuration - only Casablanca and Marrakech
        self.target_cities = {
            'casablanca': {
                'filename': 'casablanca.json',
                'city_name': 'Casablanca',
                'expected_city_id': 4  # Will be loaded from database
            },
            'marrakech': {
                'filename': 'marrakech.json',
                'city_name': 'Marrakech',
                'expected_city_id': 7  # Will be loaded from database
            }
        }

        # Processing statistics
        self.stats = {
            'cities_processed': 0,
            'hotels_processed': 0,
            'locations_created': 0,
            'accommodations_created': 0,
            'errors': [],
            'city_results': {}
        }

    def load_city_ids(self):
        """Load city IDs from database - simple and straightforward"""
        logger.info("Loading city IDs from database...")

        try:
            with self.db.get_cursor() as cursor:
                for city_key, city_config in self.target_cities.items():
                    cursor.execute(
                        "SELECT id FROM city WHERE LOWER(name) = %s AND country_id = 1",
                        (city_config['city_name'].lower(),)
                    )
                    result = cursor.fetchone()

                    if result:
                        city_config['expected_city_id'] = result['id']
                        logger.info(f"✓ Found {city_config['city_name']} with ID {result['id']}")
                    else:
                        error_msg = f"✗ City '{city_config['city_name']}' not found in database"
                        logger.error(error_msg)
                        self.stats['errors'].append(error_msg)

        except Exception as e:
            error_msg = f"Failed to load city IDs: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            raise

    def get_hotel_accommodation_type_id(self) -> int:
        """Get the Hotel accommodation type ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("SELECT id FROM accommodation_type WHERE name = 'Hotel'")
                result = cursor.fetchone()
                if result:
                    logger.info(f"✓ Found Hotel accommodation type with ID {result['id']}")
                    return result['id']
                else:
                    raise Exception("Hotel accommodation type not found in database")
        except Exception as e:
            logger.error(f"Failed to get Hotel accommodation type: {e}")
            raise

    def process_single_city(self, city_key: str) -> Dict:
        """Process a single city - simple and focused approach"""
        city_config = self.target_cities[city_key]
        logger.info(f"\n{'='*60}")
        logger.info(f"PROCESSING CITY: {city_config['city_name'].upper()}")
        logger.info(f"{'='*60}")

        city_stats = {
            'city_name': city_config['city_name'],
            'hotels_found': 0,
            'hotels_processed': 0,
            'locations_created': 0,
            'accommodations_created': 0,
            'errors': [],
            'status': 'starting'
        }

        try:
            # Check if city file exists
            city_file = self.data_dir / city_config['filename']
            if not city_file.exists():
                error_msg = f"City file not found: {city_file}"
                logger.error(error_msg)
                city_stats['errors'].append(error_msg)
                city_stats['status'] = 'failed'
                return city_stats

            # Load and validate JSON data
            logger.info(f"Loading data from {city_config['filename']}...")
            with open(city_file, 'r', encoding='utf-8') as f:
                city_data = json.load(f)

            # Simple data structure validation
            if not isinstance(city_data, dict):
                error_msg = f"Invalid JSON structure in {city_config['filename']}"
                logger.error(error_msg)
                city_stats['errors'].append(error_msg)
                city_stats['status'] = 'failed'
                return city_stats

            # Extract hotels from the actual structure: hotel IDs as root keys
            # Each key is a hotel_id, and the value contains hotel_details
            hotels_data = []
            for hotel_id_key, hotel_entry in city_data.items():
                if isinstance(hotel_entry, dict) and 'hotel_details' in hotel_entry:
                    # Add the hotel_id to the hotel_details for reference
                    hotel_details = hotel_entry['hotel_details'].copy()
                    hotel_details['external_hotel_id'] = hotel_entry.get('hotel_id', hotel_id_key)
                    hotel_details['distance_label'] = hotel_entry.get('distance_label')
                    hotels_data.append(hotel_details)

            city_stats['hotels_found'] = len(hotels_data)
            logger.info(f"Found {city_stats['hotels_found']} hotels in {city_config['city_name']}")

            if city_stats['hotels_found'] == 0:
                logger.warning(f"No hotels found in {city_config['filename']}")
                city_stats['status'] = 'completed'
                return city_stats

            # Get accommodation type ID
            hotel_type_id = self.get_hotel_accommodation_type_id()

            # Process each hotel
            for hotel_index, hotel_data in enumerate(hotels_data):
                try:
                    logger.info(f"Processing hotel {hotel_index + 1}/{city_stats['hotels_found']}: {hotel_data.get('name', 'Unknown')}")

                    # Process this hotel
                    result = self.process_single_hotel(
                        hotel_data,
                        city_config['expected_city_id'],
                        hotel_type_id
                    )

                    if result['success']:
                        city_stats['hotels_processed'] += 1
                        city_stats['locations_created'] += result['location_created']
                        city_stats['accommodations_created'] += result['accommodation_created']
                        logger.info(f"✓ Hotel processed successfully")
                    else:
                        city_stats['errors'].append(f"Hotel {hotel_index + 1}: {result['error']}")
                        logger.error(f"✗ Hotel processing failed: {result['error']}")

                except Exception as e:
                    error_msg = f"Hotel {hotel_index + 1} processing error: {e}"
                    logger.error(error_msg)
                    city_stats['errors'].append(error_msg)

            # Commit all changes for this city
            self.db.connection.commit()
            city_stats['status'] = 'completed'
            logger.info(f"✓ {city_config['city_name']} processing completed successfully")
            logger.info(f"  Hotels processed: {city_stats['hotels_processed']}/{city_stats['hotels_found']}")
            logger.info(f"  Locations created: {city_stats['locations_created']}")
            logger.info(f"  Accommodations created: {city_stats['accommodations_created']}")

        except Exception as e:
            # Rollback on error
            self.db.connection.rollback()
            error_msg = f"City {city_config['city_name']} processing failed: {e}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            city_stats['errors'].append(error_msg)
            city_stats['status'] = 'failed'

        return city_stats

    def process_single_hotel(self, hotel_data: Dict, city_id: int, hotel_type_id: int) -> Dict:
        """Process a single hotel - create location and accommodation records"""
        result = {
            'success': False,
            'location_created': 0,
            'accommodation_created': 0,
            'error': None
        }

        try:
            # Extract basic hotel information
            hotel_name = hotel_data.get('name', '').strip()
            if not hotel_name:
                result['error'] = "Hotel name is missing"
                return result

            # Extract coordinates (no address info in this structure)
            coordinates = hotel_data.get('coordinates', {})
            latitude = coordinates.get('latitude')
            longitude = coordinates.get('longitude')

            # Convert coordinates to Decimal if they exist
            if latitude is not None:
                try:
                    latitude = Decimal(str(latitude))
                except:
                    latitude = None

            if longitude is not None:
                try:
                    longitude = Decimal(str(longitude))
                except:
                    longitude = None

            # Create location record using actual database schema
            with self.db.get_cursor() as cursor:
                location_insert = """
                    INSERT INTO location (
                        name, address, city_id, lat, lng, type, place_id,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, now(), now())
                    RETURNING id
                """

                # Convert coordinates to float for lat/lng fields
                lat_float = float(latitude) if latitude is not None else None
                lng_float = float(longitude) if longitude is not None else None

                cursor.execute(location_insert, (
                    hotel_name,  # Use hotel name as location name
                    None,  # No address in this data structure
                    city_id,
                    lat_float,  # lat field (double precision)
                    lng_float,  # lng field (double precision)
                    'hotel',  # type field (required)
                    None  # place_id (optional)
                ))

                location_result = cursor.fetchone()
                if not location_result:
                    result['error'] = "Failed to create location - no result returned"
                    return result

                location_id = location_result['id']
                result['location_created'] = 1

                logger.debug(f"  ✓ Location created with ID {location_id}")

                # Extract accommodation details from actual JSON structure
                # Stars are in 'hotel_stars' field
                stars = hotel_data.get('hotel_stars')
                if stars is not None:
                    try:
                        stars = int(stars)
                        if stars < 1 or stars > 5:
                            stars = None
                    except:
                        stars = None

                # Rating is in nested 'rating' object with 'rating' field
                # Database expects numeric(3,1) so max 99.9
                rating_info = hotel_data.get('rating', {})
                average_score = None
                if isinstance(rating_info, dict):
                    rating_value = rating_info.get('rating')
                    if rating_value is not None:
                        try:
                            average_score = Decimal(str(rating_value))
                            # Ensure it fits numeric(3,1) constraint
                            if average_score < 0 or average_score > 10:
                                average_score = None
                            else:
                                # Round to 1 decimal place for numeric(3,1)
                                average_score = average_score.quantize(Decimal('0.1'))
                        except:
                            average_score = None

                # No description, website, phone, email in this JSON structure
                description = None
                website = None
                phone = None
                email = None

                # Extract construction year
                construction_year = hotel_data.get('construction_year')
                if construction_year is not None:
                    try:
                        construction_year = int(construction_year)
                        if construction_year < 1800 or construction_year > 2030:
                            construction_year = None
                    except:
                        construction_year = None

                # Extract highlights
                highlights = hotel_data.get('highlights', [])
                if not isinstance(highlights, list):
                    highlights = []

                # Create accommodation record
                accommodation_insert = """
                    INSERT INTO accommodation (
                        name, location_id, type_id, stars, average_score,
                        description, website, phone, email, construction_year,
                        highlights, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, now(), now())
                    RETURNING id
                """

                cursor.execute(accommodation_insert, (
                    hotel_name,
                    location_id,
                    hotel_type_id,
                    stars,
                    average_score,
                    description or None,
                    website or None,
                    phone or None,
                    email or None,
                    construction_year,
                    highlights if highlights else None
                ))

                accommodation_result = cursor.fetchone()
                if not accommodation_result:
                    result['error'] = "Failed to create accommodation - no result returned"
                    return result

                accommodation_id = accommodation_result['id']
                result['accommodation_created'] = 1

                logger.debug(f"  ✓ Accommodation created with ID {accommodation_id}")

                result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"  ✗ Hotel processing error: {e}")
            logger.error(f"  ✗ Hotel data that caused error: {hotel_data.get('name', 'Unknown')}")
            logger.error(traceback.format_exc())

        return result

    def run_phase3(self) -> Dict:
        """Execute complete Phase 3 core data loading"""
        logger.info("="*80)
        logger.info("STARTING PHASE 3: CORE HOTEL DATA LOADING")
        logger.info("Target cities: Casablanca and Marrakech only")
        logger.info("="*80)

        try:
            # Step 1: Load city IDs from database
            self.load_city_ids()

            # Check if we have valid city IDs
            valid_cities = [
                city_key for city_key, config in self.target_cities.items()
                if config['expected_city_id'] is not None
            ]

            if not valid_cities:
                error_msg = "No valid cities found in database"
                logger.error(error_msg)
                self.stats['errors'].append(error_msg)
                return {
                    'status': 'failed',
                    'error': error_msg,
                    'statistics': self.stats
                }

            logger.info(f"Processing {len(valid_cities)} cities: {[self.target_cities[c]['city_name'] for c in valid_cities]}")

            # Step 2: Process each city one by one
            for city_key in valid_cities:
                city_result = self.process_single_city(city_key)
                self.stats['city_results'][city_key] = city_result

                if city_result['status'] == 'completed':
                    self.stats['cities_processed'] += 1
                    self.stats['hotels_processed'] += city_result['hotels_processed']
                    self.stats['locations_created'] += city_result['locations_created']
                    self.stats['accommodations_created'] += city_result['accommodations_created']

                # Add city errors to overall errors
                self.stats['errors'].extend([
                    f"{city_result['city_name']}: {error}"
                    for error in city_result['errors']
                ])

            # Compile final results
            results = {
                'status': 'success',
                'cities_processed': self.stats['cities_processed'],
                'total_hotels_processed': self.stats['hotels_processed'],
                'total_locations_created': self.stats['locations_created'],
                'total_accommodations_created': self.stats['accommodations_created'],
                'city_results': self.stats['city_results'],
                'statistics': self.stats
            }

            logger.info("="*80)
            logger.info("PHASE 3 COMPLETED SUCCESSFULLY")
            logger.info(f"Cities processed: {self.stats['cities_processed']}")
            logger.info(f"Hotels processed: {self.stats['hotels_processed']}")
            logger.info(f"Locations created: {self.stats['locations_created']}")
            logger.info(f"Accommodations created: {self.stats['accommodations_created']}")
            logger.info("="*80)

            return results

        except Exception as e:
            logger.error(f"Phase 3 failed: {e}")
            logger.error(traceback.format_exc())
            self.stats['errors'].append(f"Phase 3 failure: {e}")

            return {
                'status': 'failed',
                'error': str(e),
                'statistics': self.stats
            }

def main():
    """Main execution function"""

    # Database configuration - UPDATE THESE VALUES
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'your_database_name',
        'user': 'your_username',
        'password': 'your_password',
        'port': 5432
    }

    # Data directory path - UPDATE THIS PATH
    DATA_DIRECTORY = "processed_data"

    # Initialize database connection
    db_connection = DatabaseConnection(**DB_CONFIG)

    try:
        # Connect to database
        db_connection.connect()

        # Initialize Phase 3 processor
        processor = SimpleCityProcessor(db_connection, DATA_DIRECTORY)

        # Execute Phase 3
        results = processor.run_phase3()

        # Print results
        print("\n" + "="*80)
        print("PHASE 3 CORE DATA LOADING RESULTS")
        print("="*80)
        print(f"Status: {results['status']}")

        if results['status'] == 'success':
            print(f"Cities processed: {results['cities_processed']}")
            print(f"Hotels processed: {results['total_hotels_processed']}")
            print(f"Locations created: {results['total_locations_created']}")
            print(f"Accommodations created: {results['total_accommodations_created']}")

            print("\nCity-by-city results:")
            for city_key, city_result in results['city_results'].items():
                print(f"\n{city_result['city_name']}:")
                print(f"  Status: {city_result['status']}")
                print(f"  Hotels found: {city_result['hotels_found']}")
                print(f"  Hotels processed: {city_result['hotels_processed']}")
                print(f"  Locations created: {city_result['locations_created']}")
                print(f"  Accommodations created: {city_result['accommodations_created']}")
                if city_result['errors']:
                    print(f"  Errors: {len(city_result['errors'])}")
                    for error in city_result['errors'][:3]:  # Show first 3 errors
                        print(f"    - {error}")

            print("\nPhase 3 completed successfully!")
        else:
            print(f"Phase 3 failed: {results.get('error', 'Unknown error')}")
            if results['statistics']['errors']:
                print(f"\nErrors encountered: {len(results['statistics']['errors'])}")
                for error in results['statistics']['errors']:
                    print(f"  - {error}")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Main execution failed: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)

    finally:
        db_connection.close()

if __name__ == "__main__":
    main()