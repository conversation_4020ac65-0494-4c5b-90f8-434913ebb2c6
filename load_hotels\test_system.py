#!/usr/bin/env python3
"""
Consolidated Hotel Data Loading System - System Test
Quick test to validate the system setup and prerequisites.
"""

import sys
import os
import logging

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import HotelLoadingConfig
from database import DatabaseManager

def test_system():
    """Test the consolidated hotel loading system"""
    print("🔍 TESTING CONSOLIDATED HOTEL DATA LOADING SYSTEM")
    print("="*60)

    try:
        # Test configuration
        print("\n1. Testing Configuration...")
        config = HotelLoadingConfig()
        print(f"   ✅ Database: {config.DATABASE_CONFIG['database']}")
        print(f"   ✅ Target Cities: {', '.join([city['city_name'] for city in config.TARGET_CITIES.values()])}")
        print(f"   ✅ Phases: {', '.join([phase for phase, settings in config.PHASES.items() if settings['enabled']])}")

        # Test database connection
        print("\n2. Testing Database Connection...")
        db_manager = DatabaseManager(config.DATABASE_CONFIG)

        if db_manager.test_connection():
            print("   ✅ Database connection successful")

            # Test city ID loading
            print("\n3. Testing City ID Loading...")
            try:
                target_cities = db_manager.load_city_ids(config.TARGET_CITIES)
                for city_key, city_config in target_cities.items():
                    print(f"   ✅ {city_config['city_name']}: ID {city_config['city_id']}")
            except Exception as e:
                print(f"   ❌ City ID loading failed: {e}")
                return False

            # Test database statistics
            print("\n4. Testing Database Statistics...")
            stats = db_manager.get_database_stats()
            print(f"   📊 Total accommodations: {stats.get('total_accommodations', 0)}")
            print(f"   📊 With external_id: {stats.get('accommodations_with_external_id', 0)}")
            print(f"   📊 With phone: {stats.get('with_phone', 0)}")
            print(f"   📊 With website: {stats.get('with_website', 0)}")
            print(f"   📊 With address: {stats.get('with_address', 0)}")
            print(f"   📊 Amenity mappings: {stats.get('amenity_mappings', 0)}")
            print(f"   📊 Reviews: {stats.get('reviews', 0)}")

        else:
            print("   ❌ Database connection failed")
            return False

        # Test data file validation
        print("\n5. Testing Data File Validation...")
        validation_results = config.validate_data_files()

        all_files_valid = True
        for city_key, city_results in validation_results.items():
            city_config = config.get_city_config(city_key)
            print(f"   [CITY] {city_config['city_name']}:")

            for data_type, exists in city_results.items():
                status = "[OK]" if exists else "[MISSING]"
                print(f"     {status} {data_type}")
                if not exists:
                    all_files_valid = False

        if not all_files_valid:
            print("\n   [WARNING] Some data files are missing - system may not run completely")
        else:
            print("\n   [OK] All required data files found")

        # Test imports
        print("\n6. Testing Module Imports...")
        try:
            from core_data_loader import CoreDataLoader
            print("   [OK] CoreDataLoader imported successfully")

            from amenities_reviews_loader import AmenitiesReviewsLoader
            print("   [OK] AmenitiesReviewsLoader imported successfully")

            from contacts_loader import ContactsLoader
            print("   [OK] ContactsLoader imported successfully")

            from load_main import HotelDataOrchestrator
            print("   [OK] HotelDataOrchestrator imported successfully")

        except Exception as e:
            print(f"   [ERROR] Import failed: {e}")
            return False

        # Test orchestrator initialization
        print("\n7. Testing Orchestrator Initialization...")
        try:
            orchestrator = HotelDataOrchestrator()
            print("   [OK] HotelDataOrchestrator initialized successfully")
        except Exception as e:
            print(f"   [ERROR] Orchestrator initialization failed: {e}")
            return False

        # Close database connection
        db_manager.close()

        print("\n" + "="*60)
        print("[SUCCESS] SYSTEM TEST COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("[OK] All components are working correctly")
        print("[READY] Ready to run: python load_main.py")
        print("="*60)

        return True

    except Exception as e:
        print(f"\n[ERROR] System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_system()

    if success:
        print("\n[SUCCESS] System is ready for hotel data loading!")
    else:
        print("\n[ERROR] System has issues that need to be resolved.")
        print("[INFO] Please check the error messages above.")

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
