#!/usr/bin/env python3
"""
Test script to verify database schema compatibility
Tests the actual database operations without processing all hotels
"""

import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from decimal import Decimal

def test_database_schema():
    """Test database schema compatibility"""
    
    # Database configuration - UPDATE THESE VALUES
    DB_CONFIG = {
        'host': 'localhost',
        'database': 'testing_full_database',
        'user': 'postgres',
        'password': '1234',
        'port': 5432
    }
    
    print("🔧 TESTING DATABASE SCHEMA COMPATIBILITY")
    print("="*60)
    
    try:
        # Connect to database
        connection = psycopg2.connect(**DB_CONFIG)
        connection.autocommit = False
        
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            
            # Test 1: Check location table structure
            print("\n1. Testing location table structure...")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'location' 
                ORDER BY ordinal_position
            """)
            location_columns = cursor.fetchall()
            
            print("   Location table columns:")
            for col in location_columns:
                print(f"     {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # Test 2: Check accommodation table structure
            print("\n2. Testing accommodation table structure...")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'accommodation' 
                AND column_name IN ('average_score', 'stars', 'highlights')
                ORDER BY ordinal_position
            """)
            accommodation_columns = cursor.fetchall()
            
            print("   Key accommodation columns:")
            for col in accommodation_columns:
                print(f"     {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            # Test 3: Check cities exist
            print("\n3. Testing target cities exist...")
            cursor.execute("""
                SELECT id, name FROM city 
                WHERE LOWER(name) IN ('casablanca', 'marrakech') 
                AND country_id = 1
            """)
            cities = cursor.fetchall()
            
            if cities:
                print("   Found target cities:")
                for city in cities:
                    print(f"     {city['name']} (ID: {city['id']})")
            else:
                print("   ❌ No target cities found!")
                return False
            
            # Test 4: Check Hotel accommodation type exists
            print("\n4. Testing Hotel accommodation type...")
            cursor.execute("SELECT id, name FROM accommodation_type WHERE name = 'Hotel'")
            hotel_type = cursor.fetchone()
            
            if hotel_type:
                print(f"   ✅ Hotel type found (ID: {hotel_type['id']})")
            else:
                print("   ❌ Hotel accommodation type not found!")
                return False
            
            # Test 5: Test actual location insert
            print("\n5. Testing location insert...")
            test_city_id = cities[0]['id']
            
            location_insert = """
                INSERT INTO location (
                    name, address, city_id, lat, lng, type, place_id,
                    created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, now(), now())
                RETURNING id
            """
            
            cursor.execute(location_insert, (
                'Test Hotel Location',  # name
                None,  # address
                test_city_id,  # city_id
                33.5731,  # lat (double precision)
                -7.5898,  # lng (double precision)
                'hotel',  # type
                None  # place_id
            ))
            
            location_result = cursor.fetchone()
            if location_result:
                test_location_id = location_result['id']
                print(f"   ✅ Location insert successful (ID: {test_location_id})")
            else:
                print("   ❌ Location insert failed!")
                return False
            
            # Test 6: Test actual accommodation insert
            print("\n6. Testing accommodation insert...")
            
            accommodation_insert = """
                INSERT INTO accommodation (
                    name, location_id, type_id, stars, average_score,
                    description, website, phone, email, construction_year,
                    highlights, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, now(), now())
                RETURNING id
            """
            
            # Test with actual data types
            test_average_score = Decimal('8.5').quantize(Decimal('0.1'))
            test_highlights = ['Test Highlight 1', 'Test Highlight 2']
            
            cursor.execute(accommodation_insert, (
                'Test Hotel',  # name
                test_location_id,  # location_id
                hotel_type['id'],  # type_id
                4,  # stars
                test_average_score,  # average_score (numeric(3,1))
                'Test description',  # description
                'https://test.com',  # website
                '+212-123-456-789',  # phone
                '<EMAIL>',  # email
                2020,  # construction_year
                test_highlights  # highlights (text[])
            ))
            
            accommodation_result = cursor.fetchone()
            if accommodation_result:
                test_accommodation_id = accommodation_result['id']
                print(f"   ✅ Accommodation insert successful (ID: {test_accommodation_id})")
            else:
                print("   ❌ Accommodation insert failed!")
                return False
            
            # Clean up test data
            print("\n7. Cleaning up test data...")
            cursor.execute("DELETE FROM accommodation WHERE id = %s", (test_accommodation_id,))
            cursor.execute("DELETE FROM location WHERE id = %s", (test_location_id,))
            print("   ✅ Test data cleaned up")
            
            # Rollback to ensure no changes are committed
            connection.rollback()
            
            print("\n" + "="*60)
            print("✅ ALL DATABASE SCHEMA TESTS PASSED!")
            print("The Phase 3 code should work with your database schema.")
            print("="*60)
            
            return True
            
    except Exception as e:
        print(f"\n❌ Database schema test failed: {e}")
        import traceback
        print(traceback.format_exc())
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()

def main():
    """Main test function"""
    
    print("Please update the DB_CONFIG in this script with your database credentials.")
    print("Then run this test to verify schema compatibility before running Phase 3.")
    
    # Uncomment the line below after updating DB_CONFIG
    success = test_database_schema()
    sys.exit(0 if success else 1)
    
    print("\nTo run the test:")
    print("1. Update DB_CONFIG with your database credentials")
    print("2. Uncomment the test_database_schema() call")
    print("3. Run: python test_database_schema.py")

if __name__ == "__main__":
    main()
